#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的诊断测试
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))

def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    
    try:
        import core.diagnostics
        print("✅ 诊断模块导入成功")
        
        from core.diagnostics import StartupDiagnostics
        print("✅ StartupDiagnostics类导入成功")
        
        # 创建诊断实例
        diagnostics = StartupDiagnostics(debug_mode=True)
        print("✅ 诊断实例创建成功")
        
        # 测试日志功能
        diagnostics.log("TEST", "INFO", "测试日志消息")
        print("✅ 日志功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_access():
    """测试配置访问"""
    print("\n🔍 测试配置访问...")
    
    try:
        # 模拟可能导致NoneType错误的情况
        config_data = None
        
        # 这应该会导致"NoneType object is not subscriptable"错误
        try:
            value = config_data['some_key']
            print(f"意外成功: {value}")
        except TypeError as e:
            if "'NoneType' object is not subscriptable" in str(e):
                print("✅ 成功捕获 'NoneType object is not subscriptable' 错误")
                print(f"   错误详情: {e}")
                return True
            else:
                print(f"❌ 捕获了不同的TypeError: {e}")
                return False
        except Exception as e:
            print(f"❌ 捕获了意外的异常: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 配置访问测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🧪 简单诊断测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 2
    
    if test_basic_imports():
        success_count += 1
    
    if test_config_access():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("✅ 所有测试通过")
    else:
        print("❌ 部分测试失败")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
