#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 统一配置管理器 - 企业级配置系统
========================================
功能描述: 统一的JSON配置管理，支持验证、观察者模式和异步操作
主要方法: get(), set(), save(), load(), validate()
调用关系: 全局唯一配置管理器，替代所有其他配置系统
注意事项:
- 统一配置验证逻辑（避免多套验证）
- 观察者模式支持热更新
- 异步保存避免UI阻塞
- 配置项与参考代码2对齐
========================================
"""

import json
import logging
import base64
import asyncio
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional


# 🎯 删除复杂的键映射逻辑，使用直接的配置路径访问

# 🎯 统一配置验证规则 - 对齐参考代码2的扁平化结构
CONFIG_VALIDATION_RULES = {
    "max_concurrent_tasks": {"type": int, "default": 2},
    "start_interval": {"type": int, "default": 2},
    "start_timeout": {"type": int, "default": 12},
    "start_fail_limit": {"type": int, "default": 2},
    "task_relay_delay": {"type": int, "default": 2},

    # 🎯 任务活动检测配置
    "monitoring.task_activity_enabled": {"type": bool, "default": True},  # 启用任务活动检测
    "monitoring.task_check_interval": {"type": int, "default": 10},  # 检测间隔（秒）
    "monitoring.task_response_timeout": {"type": int, "default": 11},  # 响应超时（秒）
    "monitoring.failure_count": {"type": int, "default": 3},  # 失败触发次数
    "monitoring.auto_switch": {"type": bool, "default": True},  # 自动切换模拟器
    "monitoring.auto_reset_on_startup": {"type": bool, "default": False},  # 启动时自动重置失败计数
    "monitoring.screenshot_path": {"type": str, "default": "./screenshots/"},  # 异常截图保存路径
    "monitoring.auto_cleanup": {"type": bool, "default": True},  # 自动清理过期截图
    "monitoring.retention_days": {"type": int, "default": 7},  # 截图保留天数
    "monitoring.overlay_timestamp": {"type": bool, "default": True},  # 截图叠加时间戳
    "monitoring.overlay_emulator_id": {"type": bool, "default": True},  # 截图叠加模拟器ID
    "monitoring.overlay_error_type": {"type": bool, "default": True},  # 截图叠加错误类型
    "monitoring.overlay_failure_count": {"type": bool, "default": True},  # 截图叠加失败次数



    # 🎯 窗口排列配置
    "window_arrangement.auto_arrange_windows": {"type": bool, "default": True},
    "window_arrangement.arrange_delay": {"type": int, "default": 2000},
    "window_arrangement.window_width": {"type": int, "default": None},
    "window_arrangement.window_height": {"type": int, "default": None},
    "window_arrangement.column_spacing": {"type": int, "default": 0},
    "window_arrangement.row_spacing": {"type": int, "default": 0},
    "window_arrangement.windows_per_row": {"type": int, "default": 7},

    # 🎯 基础配置验证规则 - 统一管理所有任务共享的基础配置
    "basic_config.apk_paths": {"type": str, "default": ""},                    # APK路径：多个路径用|分割
    "basic_config.apk_packages": {"type": str, "default": ""},                # APK包名：多个包名用|分割
    "basic_config.subscription_url": {"type": str, "default": ""},            # 订阅地址：V2Ray订阅链接
    "basic_config.emulator_shared_path": {"type": str, "default": ""},        # 模拟器共享路径：文件传输路径
    "basic_config.task_timeout_seconds": {"type": int, "default": 900, "min": 1, "max": 10800}, # 任务超时时间（秒）

    # 🎯 休息功能配置验证规则 - 工作休息时间管理
    "rest_config.enabled": {"type": bool, "default": False},                  # 启用休息功能
    "rest_config.work_time_min": {"type": int, "default": 30, "min": 1, "max": 3600},   # 工作时间最小值（秒）
    "rest_config.work_time_max": {"type": int, "default": 60, "min": 1, "max": 3600},   # 工作时间最大值（秒）
    "rest_config.rest_time_min": {"type": int, "default": 5, "min": 1, "max": 300},     # 休息时间最小值（秒）
    "rest_config.rest_time_max": {"type": int, "default": 10, "min": 1, "max": 300},    # 休息时间最大值（秒）

    # 🎯 Instagram私信配置 - 验证规则定义
    # 基础任务参数
    "instagram_dm.message_count": {"type": int, "default": 10, "min": 1, "max": 1000},  # 私信数量：每次任务发送的私信条数
    "instagram_dm.delay_min": {"type": int, "default": 5000, "min": 100, "max": 60000},         # 用户间最小延迟：发送给不同用户的最小间隔时间(毫秒)
    "instagram_dm.delay_max": {"type": int, "default": 10000, "min": 100, "max": 60000},        # 用户间最大延迟：发送给不同用户的最大间隔时间(毫秒)
    "instagram_dm.message_delay": {"type": int, "default": 2000, "min": 100, "max": 10000},     # 消息间延迟：发送单条消息后的等待时间(毫秒)

    # 任务控制参数
    "instagram_dm.recall_before_dm": {"type": bool, "default": False},                  # 私信前撤回：是否在发送私信前撤回之前的消息
    "instagram_dm.record_file_path": {"type": str, "default": "sent_users.txt"},        # 记录文件路径：保存已发送用户列表的文件路径

    # 消息内容参数
    "instagram_dm.message_content_1": {"type": str, "default": "hi|hello|nice to meet you"},      # 私信内容1：第一组消息模板，用|分隔多个选项
    "instagram_dm.message_content_2": {"type": str, "default": "How are you?|What's up?|Nice day"}, # 私信内容2：第二组消息模板，用|分隔多个选项
    "instagram_dm.send_mode": {"type": int, "default": 1, "min": 1, "max": 3},          # 发送模式：1=仅内容1，2=仅内容2，3=分段发送私信内容1，私信内容2

    # 🎯 Instagram关注配置 - 验证规则定义
    # 基础任务参数
    "instagram_follow.direct_follow_count": {"type": int, "default": 50, "min": 0, "max": 9999},   # 直接关注数量：直接关注目标用户的数量
    "instagram_follow.fans_follow_count": {"type": int, "default": 50, "min": 0, "max": 9999},     # 粉丝关注数量：关注目标用户粉丝的数量
    "instagram_follow.min_followers": {"type": int, "default": 1, "min": 1, "max": 999999},        # 粉丝数筛选：最少粉丝数要求

    # 延迟时间参数 - 去除限制，支持0-60000毫秒
    "instagram_follow.switch_delay_min": {"type": int, "default": 100, "min": 0, "max": 60000},   # 切换用户最小延迟：切换用户的最小间隔时间(毫秒)
    "instagram_follow.switch_delay_max": {"type": int, "default": 2000, "min": 0, "max": 60000},  # 切换用户最大延迟：切换用户的最大间隔时间(毫秒)
    "instagram_follow.follow_delay_min": {"type": int, "default": 100, "min": 0, "max": 60000},   # 关注最小延迟：关注操作的最小间隔时间(毫秒)
    "instagram_follow.follow_delay_max": {"type": int, "default": 2000, "min": 0, "max": 60000},  # 关注最大延迟：关注操作的最大间隔时间(毫秒)

    # 筛选控制参数
    "instagram_follow.skip_verified": {"type": bool, "default": True},                             # 蓝V用户跳过：是否跳过已认证的蓝V用户
    "instagram_follow.skip_private": {"type": bool, "default": True},                              # 私密用户跳过：是否跳过私密账户用户

    # 地区选择参数
    "instagram_follow.all_regions": {"type": bool, "default": True},                               # 所有地区：是否包含所有地区的用户
    "instagram_follow.china": {"type": bool, "default": False},                                    # 中国：是否包含中国地区的用户
    "instagram_follow.japan": {"type": bool, "default": False},                                    # 日本：是否包含日本地区的用户
    "instagram_follow.korea": {"type": bool, "default": False},                                    # 韩国：是否包含韩国地区的用户
    "instagram_follow.thailand": {"type": bool, "default": False},                                 # 泰国：是否包含泰国地区的用户

    # 文件路径参数
    "instagram_follow.follow_users_path": {"type": str, "default": "guanzhu.txt"},            # 关注用户路径：包含要关注用户列表的文件路径
}


class UnifiedConfigManager:
    """统一配置管理器 - 企业级配置系统"""

    def __init__(self, config_file: str = "app_config.json"):
        # 🎯 使用资源管理器获取配置文件路径，兼容打包环境
        try:
            from .resource_manager import get_config_path
            self.config_file = Path(get_config_path(config_file))
        except Exception:
            # 回退方案：使用全局函数
            try:
                import builtins
                if hasattr(builtins, 'get_resource_path'):
                    self.config_file = Path(builtins.get_resource_path(config_file))
                else:
                    self.config_file = Path(config_file)
            except Exception:
                self.config_file = Path(config_file)

        self.config = {}
        self.logger = logging.getLogger(self.__class__.__name__)
        self.observers: List[Callable[[str, Any, Any], None]] = []  # 观察者列表
        self._save_lock: Optional[asyncio.Lock] = None  # 延迟初始化
        self.load()
        # 🎯 删除重复日志，统一在main.py中输出
        # self.logger.info("统一配置管理器已创建")

    def load(self) -> bool:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                # 反序列化配置，处理QByteArray
                self.config = self._deserialize_config(loaded_config)
                self.logger.info(f"配置加载成功: {self.config_file}")
                return True
            else:
                # 创建默认配置
                self.config = self._get_default_config()
                self.save()
                self.logger.info(f"创建默认配置: {self.config_file}")
                return True
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            self.config = self._get_default_config()
            return False
    
    def save(self) -> bool:
        """同步保存配置文件"""
        try:
            # 序列化配置，处理QByteArray
            serialized_config = self._serialize_config(self.config)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(serialized_config, f, indent=2, ensure_ascii=False)
            self.logger.info(f"配置保存成功: {self.config_file}")
            return True
        except Exception as e:
            self.logger.error(f"配置保存失败: {e}")
            return False

    async def save_async(self) -> bool:
        """异步保存配置文件 - 避免UI阻塞"""
        # 延迟初始化异步锁
        if self._save_lock is None:
            try:
                self._save_lock = asyncio.Lock()
            except RuntimeError:
                return self.save()  # 没有事件循环，回退到同步保存

        async with self._save_lock:
            try:
                # 在线程池中执行IO操作
                loop = asyncio.get_event_loop()
                serialized_config = self._serialize_config(self.config)

                def _write_file():
                    with open(self.config_file, 'w', encoding='utf-8') as f:
                        json.dump(serialized_config, f, indent=2, ensure_ascii=False)

                await loop.run_in_executor(None, _write_file)
                self.logger.info(f"配置异步保存成功: {self.config_file}")
                return True
            except Exception as e:
                self.logger.error(f"配置异步保存失败: {e}")
                return False

    def get(self, key: str, default: Any = None) -> Any:
        """🎯 获取配置值，使用直接路径访问"""
        try:
            keys = key.split('.')
            value = self.config
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            # 如果没有找到配置，返回验证规则中的默认值
            if key in CONFIG_VALIDATION_RULES:
                return CONFIG_VALIDATION_RULES[key].get("default", default)
            return default

    def validate(self, key: str, value: Any) -> tuple[bool, str]:
        """🎯 统一配置验证逻辑 - 避免多套验证"""
        if key not in CONFIG_VALIDATION_RULES:
            return True, ""  # 未定义验证规则的配置项直接通过

        rule = CONFIG_VALIDATION_RULES[key]

        # 类型验证
        expected_type = rule.get("type")
        if expected_type and not isinstance(value, expected_type):
            return False, f"配置项 {key} 类型错误，期望 {expected_type.__name__}，实际 {type(value).__name__}"

        # 数值范围验证
        if isinstance(value, (int, float)):
            min_val = rule.get("min")
            max_val = rule.get("max")
            if min_val is not None and value < min_val:
                return False, f"配置项 {key} 值 {value} 小于最小值 {min_val}"
            if max_val is not None and value > max_val:
                return False, f"配置项 {key} 值 {value} 大于最大值 {max_val}"

        return True, ""

    def set(self, key: str, value: Any, validate: bool = True) -> bool:
        """🎯 设置配置值，使用直接路径访问"""
        # 统一验证逻辑
        if validate:
            is_valid, error_msg = self.validate(key, value)
            if not is_valid:
                self.logger.error(f"配置验证失败: {error_msg}")
                return False

        try:
            old_value = self.get(key)
            keys = key.split('.')
            config = self.config

            # 创建嵌套结构
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]

            # 设置最终值
            config[keys[-1]] = value
            self.logger.debug(f"配置设置: {key} = {value}")

            # 通知观察者
            self._notify_observers(key, old_value, value)

            return True
        except Exception as e:
            self.logger.error(f"配置设置失败: {key} = {value}, 错误: {e}")
            return False

    def register_observer(self, callback: Callable[[str, Any, Any], None]) -> None:
        """注册配置变化观察者"""
        if callback not in self.observers:
            self.observers.append(callback)
            self.logger.debug(f"注册配置观察者: {callback.__name__}")

    def unregister_observer(self, callback: Callable[[str, Any, Any], None]) -> None:
        """注销配置变化观察者"""
        if callback in self.observers:
            self.observers.remove(callback)
            self.logger.debug(f"注销配置观察者: {callback.__name__}")

    def _notify_observers(self, key: str, old_value: Any, new_value: Any) -> None:
        """通知所有观察者配置变化"""
        for observer in self.observers:
            try:
                observer(key, old_value, new_value)
            except Exception as e:
                self.logger.error(f"通知观察者失败: {observer.__name__}, 错误: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置 - 对齐参考代码2的扁平化结构"""
        return {
            # 🎯 扁平化配置结构 - 对齐参考代码2
            "emulator_path": "G:/leidian/LDPlayer9",
            "max_concurrent_tasks": 2,
            "start_interval": 2,
            "start_timeout": 12,
            "start_fail_limit": 2,
            "task_relay_delay": 2,

            # 🎯 监控配置 - 使用新的任务活动检测配置
            "monitoring": {
                "task_activity_enabled": True,
                "task_check_interval": 10,
                "task_response_timeout": 11,
                "failure_count": 3,
                "auto_switch": True,
                "auto_reset_on_startup": False,
                "screenshot_path": "./screenshots/",
                "auto_cleanup": True,
                "retention_days": 7,
                "overlay_timestamp": True,
                "overlay_emulator_id": True,
                "overlay_error_type": True,
                "overlay_failure_count": True
            },

            # 🎯 基础配置 - 所有任务共享的基础配置默认值
            "basic_config": {
                "apk_paths": "",                                            # APK路径：多个路径用|分割
                "apk_packages": "",                                         # APK包名：多个包名用|分割
                "subscription_url": "",                                     # 订阅地址：V2Ray订阅链接
                "emulator_shared_path": "",                                 # 模拟器共享路径：文件传输路径
                "task_timeout_minutes": 15                                  # 任务超时时间：15分钟
            },

            # 🎯 休息功能配置 - 工作休息时间管理默认值
            "rest_config": {
                "enabled": False,                                           # 默认不启用休息功能
                "work_time_min": 30,                                        # 工作时间最小值：30秒
                "work_time_max": 60,                                        # 工作时间最大值：60秒
                "rest_time_min": 5,                                         # 休息时间最小值：5秒
                "rest_time_max": 10                                         # 休息时间最大值：10秒
            },

            # 🎯 Instagram私信配置 - 默认值定义
            "instagram_dm": {
                "message_count": 10,                                        # 默认私信数量：10条
                "delay_min": 5000,                                          # 默认最小延迟：5000毫秒(5秒)
                "delay_max": 10000,                                         # 默认最大延迟：10000毫秒(10秒)
                "message_delay": 2000,                                      # 默认消息间延迟：2000毫秒(2秒)
                "recall_before_dm": False,                                  # 默认不启用私信前撤回
                "record_file_path": "sent_users.txt",                       # 默认记录文件名
                "message_content_1": "hi|hello|nice to meet you",     # 默认私信内容1（用|分隔）
                "message_content_2": "How are you?|What's up?|Nice day", # 默认私信内容2（用|分隔）
                "send_mode": 1                                              # 默认发送模式：仅发送内容1
            },

            # 🎯 窗口状态配置
            "window_state": {
                "geometry": None,
                "state": None,
                "current_page": 0,
                "main_splitter_state": None,
                "bottom_splitter_state": None
            }
        }
    


    def get_validation_info(self, key: str) -> Optional[Dict[str, Any]]:
        """获取配置项的验证信息"""
        return CONFIG_VALIDATION_RULES.get(key)



    def _serialize_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """序列化配置，处理QByteArray等特殊类型"""
        def serialize_value(value):
            # 检查是否是QByteArray
            if hasattr(value, 'data') and hasattr(value, 'size'):
                # 这是QByteArray，转换为base64字符串
                try:
                    return {
                        '_type': 'QByteArray',
                        '_data': base64.b64encode(value.data()).decode('utf-8')
                    }
                except:
                    return None
            elif isinstance(value, dict):
                return {k: serialize_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [serialize_value(item) for item in value]
            else:
                return value

        return serialize_value(config)

    def _deserialize_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """反序列化配置，恢复QByteArray等特殊类型"""
        def deserialize_value(value):
            if isinstance(value, dict):
                if value.get('_type') == 'QByteArray':
                    # 恢复QByteArray
                    try:
                        from PyQt6.QtCore import QByteArray
                        data = base64.b64decode(value['_data'])
                        return QByteArray(data)
                    except:
                        return None
                else:
                    return {k: deserialize_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [deserialize_value(item) for item in value]
            else:
                return value

        return deserialize_value(config)


# 🎯 全局配置管理器实例 - 单例模式
_config_manager: Optional[UnifiedConfigManager] = None

def get_config_manager() -> UnifiedConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = UnifiedConfigManager()
    return _config_manager



