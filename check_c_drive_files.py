#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 检查C盘生成的文件
========================================
功能描述: 检查我们的程序是否在C盘生成了文件
========================================
"""

import os
import subprocess
from pathlib import Path

def check_user_config_directory():
    """检查用户配置目录"""
    print("🎯 检查用户配置目录")
    print("=" * 60)
    
    # 我们的守护进程安装路径
    config_path = Path.home() / ".config" / "system"
    
    print(f"📁 检查路径: {config_path}")
    
    if config_path.exists():
        print("✅ 配置目录存在")
        
        # 列出目录内容
        try:
            files = list(config_path.iterdir())
            if files:
                print("📋 目录内容:")
                for file in files:
                    size = file.stat().st_size if file.is_file() else "目录"
                    print(f"  • {file.name} ({size} 字节)")
            else:
                print("📋 目录为空")
        except Exception as e:
            print(f"❌ 读取目录失败: {e}")
    else:
        print("❌ 配置目录不存在")
    
    return config_path.exists()

def check_registry_entries():
    """检查注册表启动项"""
    print("\n🎯 检查注册表启动项")
    print("=" * 60)
    
    try:
        # 检查当前用户启动项
        ps_cmd = '''
$regPath = "HKCU:\\Software\\Microsoft\\Windows\\CurrentVersion\\Run"
$items = Get-ItemProperty -Path $regPath -ErrorAction SilentlyContinue
if ($items) {
    $items.PSObject.Properties | Where-Object { $_.Name -notlike "PS*" } | ForEach-Object {
        Write-Host "$($_.Name): $($_.Value)"
    }
} else {
    Write-Host "无启动项"
}
'''
        
        result = subprocess.run([
            "powershell", "-Command", ps_cmd
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            output = result.stdout.strip()
            if output:
                print("📋 当前用户启动项:")
                lines = output.split('\n')
                found_our_item = False
                
                for line in lines:
                    line = line.strip()
                    if line:
                        if any(keyword in line.lower() for keyword in ['systemhelper', 'system helper']):
                            print(f"  🎯 我们的启动项: {line}")
                            found_our_item = True
                        else:
                            print(f"  • {line}")
                
                if not found_our_item:
                    print("  ❌ 未找到我们的启动项")
            else:
                print("📋 无启动项")
        else:
            print(f"❌ 检查失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 检查注册表失败: {e}")

def check_temp_directories():
    """检查临时目录"""
    print("\n🎯 检查临时目录")
    print("=" * 60)
    
    temp_paths = [
        Path(os.environ.get('TEMP', '')),
        Path(os.environ.get('TMP', '')),
        Path("C:/Windows/Temp"),
        Path("C:/Temp")
    ]
    
    for temp_path in temp_paths:
        if temp_path.exists():
            print(f"📁 检查: {temp_path}")
            
            try:
                # 查找可能相关的文件
                suspicious_files = []
                
                for file in temp_path.iterdir():
                    if file.is_file():
                        name_lower = file.name.lower()
                        if any(keyword in name_lower for keyword in [
                            'systemhelper', 'guardian', 'python', 'pyinstaller'
                        ]):
                            suspicious_files.append(file)
                
                if suspicious_files:
                    print(f"  ⚠️  发现可疑文件:")
                    for file in suspicious_files:
                        size = file.stat().st_size
                        print(f"    • {file.name} ({size} 字节)")
                else:
                    print(f"  ✅ 无相关文件")
                    
            except PermissionError:
                print(f"  ❌ 权限不足，无法访问")
            except Exception as e:
                print(f"  ❌ 检查失败: {e}")

def check_appdata_directories():
    """检查AppData目录"""
    print("\n🎯 检查AppData目录")
    print("=" * 60)
    
    appdata_paths = [
        Path.home() / "AppData" / "Local",
        Path.home() / "AppData" / "Roaming",
        Path.home() / "AppData" / "LocalLow"
    ]
    
    for appdata_path in appdata_paths:
        if appdata_path.exists():
            print(f"📁 检查: {appdata_path}")
            
            try:
                # 查找可能的相关目录
                suspicious_dirs = []
                
                for item in appdata_path.iterdir():
                    if item.is_dir():
                        name_lower = item.name.lower()
                        if any(keyword in name_lower for keyword in [
                            'systemhelper', 'guardian', 'system'
                        ]):
                            suspicious_dirs.append(item)
                
                if suspicious_dirs:
                    print(f"  ⚠️  发现可疑目录:")
                    for dir_path in suspicious_dirs:
                        print(f"    • {dir_path.name}")
                        
                        # 检查目录内容
                        try:
                            files = list(dir_path.iterdir())
                            if files:
                                print(f"      内容: {len(files)} 个文件/目录")
                            else:
                                print(f"      内容: 空目录")
                        except:
                            print(f"      内容: 无法访问")
                else:
                    print(f"  ✅ 无相关目录")
                    
            except PermissionError:
                print(f"  ❌ 权限不足，无法访问")
            except Exception as e:
                print(f"  ❌ 检查失败: {e}")

def check_running_processes():
    """检查运行中的进程"""
    print("\n🎯 检查运行中的相关进程")
    print("=" * 60)
    
    try:
        import psutil
        
        found_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                proc_info = proc.info
                proc_name = proc_info['name'] or ''
                proc_exe = proc_info['exe'] or ''
                
                # 检查是否是我们的进程
                if any(keyword in proc_name.lower() for keyword in ['systemhelper']):
                    found_processes.append((proc_info['pid'], proc_name, proc_exe))
                elif proc_exe and any(keyword in proc_exe.lower() for keyword in ['systemhelper']):
                    found_processes.append((proc_info['pid'], proc_name, proc_exe))
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if found_processes:
            print("📋 发现相关进程:")
            for pid, name, exe in found_processes:
                print(f"  • PID: {pid}, 名称: {name}")
                print(f"    路径: {exe}")
        else:
            print("✅ 未发现相关进程")
            
    except ImportError:
        print("❌ psutil未安装，无法检查进程")
    except Exception as e:
        print(f"❌ 检查进程失败: {e}")

def main():
    """主函数"""
    print("🚀 C盘文件检查工具")
    print("=" * 80)
    print("📋 检查我们的程序是否在C盘生成了文件...")
    print("=" * 80)
    
    # 1. 检查用户配置目录
    config_exists = check_user_config_directory()
    
    # 2. 检查注册表启动项
    check_registry_entries()
    
    # 3. 检查临时目录
    check_temp_directories()
    
    # 4. 检查AppData目录
    check_appdata_directories()
    
    # 5. 检查运行中的进程
    check_running_processes()
    
    print("\n" + "=" * 80)
    print("🎯 检查总结:")
    if config_exists:
        print("⚠️  发现我们的程序在C盘生成了文件")
        print("📁 位置: %USERPROFILE%\\.config\\system\\")
        print("💡 这是守护进程的安装目录")
    else:
        print("✅ 未发现我们的程序在C盘生成文件")
    
    print("\n💡 说明:")
    print("  • 如果守护进程已安装，会在用户配置目录创建文件")
    print("  • 这是正常的程序行为")
    print("  • 卸载时会自动清理这些文件")
    print("=" * 80)

if __name__ == "__main__":
    main()
