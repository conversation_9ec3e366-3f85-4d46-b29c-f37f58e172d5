2025-07-31 11:08:01 - root - INFO - 简化日志系统初始化完成
2025-07-31 11:08:01 - main - INFO - 应用程序启动
2025-07-31 11:08:01 - __main__ - INFO - Qt应用程序已创建
2025-07-31 11:08:01 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-31 11:08:01 - __main__ - INFO - 统一配置管理器已创建
2025-07-31 11:08:01 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-31 11:08:01 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-31 11:08:01 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-31 11:08:01 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-31 11:08:01 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-31 11:08:01 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-31 11:08:01 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-31 11:08:01 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-31 11:08:01 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-31 11:08:01 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-31 11:08:01 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-31 11:08:01 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-31 11:08:01 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-31 11:08:01 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-31 11:08:01 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-31 11:08:01 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-31 11:08:01 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-31 11:08:01 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-31 11:08:02 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-31 11:08:02 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-31 11:08:02 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-31 11:08:02 - __main__ - INFO - UI主窗口已创建
2025-07-31 11:08:02 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-31 11:08:02 - __main__ - INFO - 主窗口已显示
2025-07-31 11:08:02 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-31 11:08:02 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-31 11:08:02 - __main__ - INFO - UI层和业务层已连接
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-31 11:08:02 - __main__ - INFO - 启动Qt事件循环
2025-07-31 11:08:02 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-31 11:08:02 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-31 11:08:02 - App - INFO - ldconsole命令执行成功，输出长度: 48407
2025-07-31 11:08:02 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-31 11:08:02 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-31 11:08:02 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-31 11:08:02 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-31 11:08:02 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.31s | count: 1229
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-31 11:08:02 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-31 11:08:02 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-31 11:08:02 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-31 11:08:02 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-31 11:08:02 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-31 11:08:02 - App - INFO - ldconsole命令执行成功，输出长度: 48407
2025-07-31 11:08:02 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-31 11:08:02 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-31 11:08:02 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-31 11:08:02 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-31 11:08:02 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.25s | count: 1229
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-31 11:08:02 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-31 11:08:02 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-31 11:08:02 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-31 11:08:03 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-31 11:08:03 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-31 11:08:03 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-31 11:08:03 - __main__ - INFO - 后台服务已启动
2025-07-31 11:08:03 - __main__ - INFO - 延迟启动服务完成
2025-07-31 11:08:05 - FixedAsyncBridge - INFO - 执行异步操作: instagram_dm_task
2025-07-31 11:08:05 - MainWindowV2 - INFO - Instagram粉丝私信任务已启动，涉及1个模拟器
2025-07-31 11:08:05 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram粉丝私信任务请求
2025-07-31 11:08:05 - MainWindowV2 - INFO - 用户启动Instagram粉丝私信任务，模拟器数量: 1
2025-07-31 11:08:05 - FixedAsyncBridge - INFO - 开始处理Instagram粉丝私信任务（线程池模式），模拟器数量: 1
2025-07-31 11:08:05 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [11]
2025-07-31 11:08:05 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 1
2025-07-31 11:08:05 - StartupManager - INFO - 批量启动请求 | count: 1
2025-07-31 11:08:05 - StartupManager - INFO - 启动调度器已启动
2025-07-31 11:08:05 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-31 11:08:05 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建任务线程池
2025-07-31 11:08:05 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-31 11:08:05 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-31 11:08:05 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 1
2025-07-31 11:08:05 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建1个Instagram线程
2025-07-31 11:08:05 - FixedAsyncBridge - INFO - 异步桥接器: Instagram粉丝私信任务请求已处理，状态: started
2025-07-31 11:08:05 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器11状态变化: 排队中 -> 启动中
2025-07-31 11:08:05 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 11
2025-07-31 11:08:05 - MainWindowV2 - INFO - 模拟器11: 未知 -> 排队中
2025-07-31 11:08:05 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 1 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-31 11:08:05 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-31 11:08:05 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-31 11:08:05 - MainWindowV2 - INFO - 批量启动完成: 0/1 成功
2025-07-31 11:08:05 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 1
2025-07-31 11:08:05 - FixedAsyncBridge - INFO - 异步操作完成: instagram_dm_task
2025-07-31 11:08:05 - MainWindowV2 - INFO - Instagram粉丝私信任务启动成功: Instagram粉丝私信任务已启动，涉及1个模拟器（线程池并发模式）
2025-07-31 11:08:05 - Emulator - INFO - 模拟器状态变化 | emulator_id: 11 | old_state: 排队中 | new_state: 启动中
2025-07-31 11:08:05 - MainWindowV2 - INFO - 模拟器11: 排队中 -> 启动中
2025-07-31 11:08:05 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 0 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-31 11:08:05 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-31 11:08:05 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 11
2025-07-31 11:08:05 - Emulator - INFO - Android系统启动完成 | emulator_id: 11 | elapsed_time: 0.2秒
2025-07-31 11:08:05 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器11状态变化: 启动中 -> 运行中
2025-07-31 11:08:05 - Emulator - INFO - 模拟器状态变化 | emulator_id: 11 | old_state: 启动中 | new_state: 运行中
2025-07-31 11:08:05 - InstagramTaskManager - INFO - 启动模拟器11的Instagram私信任务线程 - 当前并发: 1/1
2025-07-31 11:08:05 - TaskActivityHeartbeatManager - INFO - 模拟器 11 已添加到任务活动监控，失败计数: 0
2025-07-31 11:08:05 - Emulator - INFO - 模拟器启动成功 | emulator_id: 11 | running_count: 1
2025-07-31 11:08:05 - MainWindowV2 - INFO - 任务完成: 模拟器11, 任务start
2025-07-31 11:08:05 - MainWindowV2 - INFO - 模拟器11启动成功
2025-07-31 11:08:05 - InstagramTaskThread - INFO - [模拟器11] 开始等待启动完成
2025-07-31 11:08:05 - InstagramTaskThread - INFO - [模拟器11] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-31 11:08:05 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=11, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-31 11:08:05 - InstagramTaskThread - INFO - [模拟器11] 开始窗口排列
2025-07-31 11:08:05 - WindowArrangementManager - INFO - 模拟器11启动完成，立即触发窗口排列
2025-07-31 11:08:05 - MainWindowV2 - WARNING - 未找到模拟器11，无法更新状态
2025-07-31 11:08:05 - MainWindowV2 - INFO - 模拟器11: 启动中 -> 运行中
2025-07-31 11:08:05 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 1 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 100.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-31 11:08:05 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-31 11:08:05 - MainWindowV2 - INFO - 模拟器11: 启动中 -> 运行中, PID: 34516
2025-07-31 11:08:05 - MainWindowV2 - INFO - 模拟器11: 启动中 -> 运行中
2025-07-31 11:08:06 - StartupManager - INFO - 调度器已停止
2025-07-31 11:08:07 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-31 11:08:07 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-31 11:08:07 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-31 11:08:07 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-31 11:08:07 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-31 11:08:07 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-31 11:08:07 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-11' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-31 11:08:07 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-31 11:08:07 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-31 11:08:07 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-11' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-31 11:08:07 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-31 11:08:07 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-31 11:08:13 - InstagramTaskThread - INFO - [模拟器11] 窗口排列完成
2025-07-31 11:08:13 - InstagramTaskThread - INFO - [模拟器11] 开始执行Instagram私信任务
2025-07-31 11:08:13 - InstagramDMTask - INFO - [模拟器11] 雷电模拟器API初始化成功
2025-07-31 11:08:13 - InstagramDMTask - INFO - [模拟器11] 模拟器路径: G:/leidian/LDPlayer9
2025-07-31 11:08:13 - InstagramDMTask - INFO - [模拟器11] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-31 11:08:13 - InstagramDMTask - INFO - [模拟器11] 已设置ld.emulator_id = 11
2025-07-31 11:08:13 - InstagramDMTask - INFO - [模拟器11] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-31 11:08:13 - InstagramDMTask - INFO - [模拟器11] Instagram任务配置热加载观察者已注册
2025-07-31 11:08:13 - InstagramDMTask - INFO - [模拟器11] Instagram私信任务执行器初始化完成
2025-07-31 11:08:13 - InstagramTaskThread - INFO - [模拟器11] 任务超时计时已从线程启动开始: 666秒
2025-07-31 11:08:13 - InstagramDMTask - INFO - [模拟器11] 开始执行Instagram私信任务
2025-07-31 11:08:13 - InstagramDMTask - INFO - [模拟器11] 任务超时设置: 666秒，已运行: 8.21秒
2025-07-31 11:08:14 - InstagramDMTask - INFO - [模拟器11] 模拟器Android系统运行正常，桌面稳定
2025-07-31 11:08:14 - InstagramDMTask - INFO - [模拟器11] 开始检查应用安装状态
2025-07-31 11:08:14 - InstagramDMTask - INFO - [模拟器11] ✅ V2Ray已安装，版本: 1.1.12
2025-07-31 11:08:14 - InstagramDMTask - INFO - [模拟器11] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-31 11:08:14 - InstagramDMTask - INFO - [模拟器11] 📊 应用安装状态检测结果:
2025-07-31 11:08:14 - InstagramDMTask - INFO - [模拟器11] ✅ 所有必要应用已安装
2025-07-31 11:08:14 - InstagramDMTask - INFO - [模拟器11] 开始启动V2Ray应用
2025-07-31 11:08:14 - InstagramDMTask - INFO - [模拟器11] V2Ray启动命令执行成功，等待应用加载
2025-07-31 11:08:14 - InstagramDMTask - INFO - [模拟器11] V2Ray应用启动结果: 成功
2025-07-31 11:08:17 - InstagramDMTask - INFO - [模拟器11] ✅ V2Ray应用启动成功
2025-07-31 11:08:17 - InstagramDMTask - INFO - [模拟器11] 开始检查V2Ray节点列表状态
2025-07-31 11:08:18 - InstagramDMTask - INFO - [模拟器11] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-31 11:08:18 - InstagramDMTask - INFO - [模拟器11] 开始连接V2Ray节点
2025-07-31 11:08:20 - InstagramDMTask - INFO - [模拟器11] 当前连接状态: 已连接，点击测试连接
2025-07-31 11:08:20 - InstagramDMTask - INFO - [模拟器11] V2Ray节点已连接，无需重复连接
2025-07-31 11:08:20 - InstagramDMTask - INFO - [模拟器11] 开始测试V2Ray节点延迟
2025-07-31 11:08:20 - InstagramDMTask - INFO - [模拟器11] 开始V2Ray节点延迟测试
2025-07-31 11:08:21 - InstagramDMTask - INFO - [模拟器11] 当前测试状态: 已连接，点击测试连接
2025-07-31 11:08:21 - InstagramDMTask - INFO - [模拟器11] 点击开始延迟测试
2025-07-31 11:08:21 - InstagramDMTask - INFO - [模拟器11] 已点击测试按钮，等待测试结果
2025-07-31 11:08:21 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-31 11:08:21 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-31 11:08:21 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 11
2025-07-31 11:08:21 - MainWindowV2 - WARNING - 模拟器11心跳状态更新未产生变化
2025-07-31 11:08:21 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-31 11:08:23 - InstagramDMTask - INFO - [模拟器11] 测试状态监控 (1/30): 失败：: io: read/write on closed pipe
2025-07-31 11:08:23 - InstagramDMTask - ERROR - [模拟器11] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: io: read/write on closed pipe
2025-07-31 11:08:23 - InstagramDMTask - INFO - [模拟器11] 点击失败状态重置UI
2025-07-31 11:08:23 - InstagramDMTask - INFO - [模拟器11] 已点击失败状态，等待UI重置
2025-07-31 11:08:23 - InstagramDMTask - INFO - [模拟器11] 继续等待测试结果 (1/3)
2025-07-31 11:08:24 - InstagramDMTask - INFO - [模拟器11] ✅ V2Ray节点延迟测试成功: 连接成功：延时 260 毫秒
2025-07-31 11:08:24 - InstagramDMTask - INFO - [模拟器11] 等待5秒后进入下一阶段
2025-07-31 11:08:29 - InstagramDMTask - INFO - [模拟器11] 开始启动Instagram应用
2025-07-31 11:08:30 - InstagramDMTask - INFO - [模拟器11] Instagram启动命令执行成功，等待应用加载
2025-07-31 11:08:33 - InstagramDMTask - INFO - [模拟器11] ✅ Instagram应用启动命令执行完成
2025-07-31 11:08:33 - InstagramDMTask - INFO - [模拟器11] Instagram启动检测 第1/5次
2025-07-31 11:08:35 - InstagramDMTask - INFO - [模拟器11] ❌ 批量验证失败
2025-07-31 11:08:35 - InstagramDMTask - INFO - [模拟器11] ❌ 验证失败
2025-07-31 11:08:38 - InstagramDMTask - INFO - [模拟器11] Instagram启动检测中... 已等待4.9秒
2025-07-31 11:08:40 - InstagramDMTask - INFO - [模拟器11] ✅ 批量验证成功
2025-07-31 11:08:40 - InstagramDMTask - INFO - [模拟器11] ✅ 验证成功
2025-07-31 11:08:40 - InstagramDMTask - INFO - [模拟器11] Instagram页面状态检测结果: 正常-在主页面
2025-07-31 11:08:40 - InstagramDMTask - INFO - [模拟器11] ✅ Instagram已在主页面，可以继续执行任务
2025-07-31 11:08:40 - InstagramDMTask - INFO - [模拟器11] 开始导航到个人主页
2025-07-31 11:08:41 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-31 11:08:41 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-31 11:08:41 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 11
2025-07-31 11:08:41 - MainWindowV2 - WARNING - 模拟器11心跳状态更新未产生变化
2025-07-31 11:08:41 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-31 11:08:49 - InstagramDMTask - ERROR - [模拟器11] 未找到个人主页标识
2025-07-31 11:08:49 - InstagramTaskThread - ERROR - [模拟器11] Instagram任务执行失败: 导航到个人主页失败
2025-07-31 11:09:01 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-31 11:09:01 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-31 11:09:01 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 11
2025-07-31 11:09:01 - MainWindowV2 - WARNING - 模拟器11心跳状态更新未产生变化
2025-07-31 11:09:01 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-31 11:09:10 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-31 11:09:10 - __main__ - INFO - 开始清理资源...
2025-07-31 11:09:10 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-31 11:09:10 - __main__ - INFO - 配置已保存
2025-07-31 11:09:10 - __main__ - ERROR - 异步桥梁关闭失败: 'FixedAsyncBridge' object has no attribute 'shutdown'
2025-07-31 11:09:10 - __main__ - INFO - 应用程序已退出
