================================================================================
🚀 程序启动日志
================================================================================
启动时间: 2025-08-02 11:47:11.245830
Python版本: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
工作目录: E:\VScodexiangmu\insleidian2
命令行参数: ['e:/VScodexiangmu/insleidian2/main.py']
错误日志文件: error_logs\crash_report_20250802_114711.log
================================================================================

[11:47:11.245] ✅ INIT: 程序启动开始
[11:47:11.246] ✅ ARGS: 开始解析命令行参数
[11:47:11.255] ✅ ARGS: 命令行参数解析完成: debug=False, no_ui=False
[11:47:11.256] ✅ DIAG: 开始启动诊断
[11:47:11.256] ✅ DIAG: 启动诊断完成
[11:47:11.257] ✅ AUTH: 开始认证验证
[11:47:11.321] ✅ AUTH: 临时QApplication创建成功
[11:47:16.927] ✅ AUTH: 认证成功
[11:47:16.928] ✅ AUTH: 临时QApplication已退出
[11:47:16.928] ✅ IMPORT: 开始导入PyQt6组件
[11:47:16.929] ✅ IMPORT: PyQt6组件导入成功
[11:47:16.930] ✅ LOG: 开始设置日志系统
[11:47:16.938] ✅ LOG: 日志系统设置完成
[11:47:16.938] ✅ APP: 开始创建Qt应用程序
[11:47:16.940] ✅ APP: Qt应用程序创建成功
[11:47:16.940] ✅ CORE: 开始创建核心组件
[11:47:17.004] ✅ CORE: 核心组件创建成功
[11:47:17.006] ✅ UI: 开始创建UI组件
[11:47:17.368] ✅ UI: UI组件创建成功
[11:47:17.369] ✅ SHOW: 开始显示主窗口
[11:47:17.460] ✅ SHOW: 主窗口显示成功
[11:47:17.461] ✅ EVENT: 处理待处理事件
[11:47:17.467] ✅ EVENT: 事件处理完成
[11:47:17.467] ✅ CONNECT: 开始连接组件
[11:47:17.524] ✅ CONNECT: 组件连接成功
[11:47:17.524] ✅ TIMER: 设置延迟启动定时器
[11:47:17.525] ✅ LOOP: 开始Qt事件循环
[11:47:18.526] ✅ SERVICE: 开始启动延迟服务
[11:47:18.530] ✅ SERVICE: 延迟服务启动成功
[11:47:19.663] ✅ CLEANUP: 开始清理资源
[11:47:19.665] ✅ CLEANUP: 资源清理完成
[11:47:19.665] ✅ EXIT: 程序正常退出，退出码: 0
