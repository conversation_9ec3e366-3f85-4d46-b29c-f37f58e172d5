#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试诊断功能的脚本
模拟可能导致"NoneType object is not subscriptable"错误的情况
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))

def test_config_none_error():
    """测试配置为None时的错误"""
    print("🔍 测试配置为None的情况...")
    
    try:
        from core.simple_config import get_config_manager
        
        # 获取配置管理器
        config_manager = get_config_manager()
        
        # 尝试访问可能为None的配置
        test_keys = [
            'emulator.default_id',
            'instagram.username', 
            'task.max_retries',
            'nonexistent.key'
        ]
        
        for key in test_keys:
            try:
                value = config_manager.get(key)
                print(f"✅ 配置项 {key}: {value} (类型: {type(value)})")
            except Exception as e:
                print(f"❌ 配置项 {key} 访问失败: {e}")
        
        # 测试强制设置config为None的情况
        print("\n🔍 测试强制设置config为None...")
        original_config = config_manager.config
        config_manager.config = None
        
        try:
            value = config_manager.get('test.key', 'default_value')
            print(f"✅ config为None时获取配置: {value}")
        except Exception as e:
            print(f"❌ config为None时访问失败: {e}")
        finally:
            # 恢复原始配置
            config_manager.config = original_config
            
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_instagram_task_none_error():
    """测试Instagram任务中可能的None错误"""
    print("\n🔍 测试Instagram任务模块...")
    
    try:
        from core.instagram_task import InstagramDMTask
        
        # 尝试创建任务实例
        task = InstagramDMTask(emulator_id=0)
        print("✅ InstagramDMTask实例创建成功")
        
        # 测试可能导致None错误的方法
        print("🔍 测试任务方法...")
        
        # 这些方法可能会访问None值
        test_methods = [
            ('get_emulator_id', lambda: task.emulator_id),
            ('get_config', lambda: getattr(task, 'config', None)),
        ]
        
        for method_name, method_func in test_methods:
            try:
                result = method_func()
                print(f"✅ {method_name}: {result}")
            except Exception as e:
                print(f"❌ {method_name} 失败: {e}")
                
    except Exception as e:
        print(f"❌ Instagram任务测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_leidian_api_none_error():
    """测试雷电API中可能的None错误"""
    print("\n🔍 测试雷电API模块...")
    
    try:
        from core.leidianapi.yijianzhaotu import find_image_position
        
        # 测试图像查找功能
        print("✅ 一键找图模块导入成功")
        
        # 测试可能导致None错误的调用
        try:
            # 使用不存在的图片路径测试
            result = find_image_position('nonexistent.png', 'test_window')
            print(f"✅ 图像查找测试: {result}")
        except Exception as e:
            print(f"⚠️ 图像查找预期失败: {e}")
            
    except Exception as e:
        print(f"❌ 雷电API测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 启动诊断功能测试")
    print("=" * 60)
    
    # 首先运行完整诊断
    print("🔍 运行完整启动诊断...")
    try:
        from core.diagnostics import run_startup_diagnostics
        
        diagnostics = run_startup_diagnostics(debug_mode=True)
        diagnostics.print_summary()
        
    except Exception as e:
        print(f"❌ 启动诊断失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🧪 具体错误场景测试")
    print("=" * 60)
    
    # 测试具体的错误场景
    test_config_none_error()
    test_instagram_task_none_error()
    test_leidian_api_none_error()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
