=== 启动诊断日志 ===
开始时间: 2025-08-01 21:36:01.485878
Python版本: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
工作目录: E:\VScodexiangmu\insleidian2
==================================================

[STARTUP-DEBUG] [21:36:01.486] [STARTUP] 启动诊断模式已激活
[STARTUP-INFO] [21:36:01.486] [系统环境] 开始检查组件: 系统环境
[STARTUP-SUCCESS] [21:36:01.486] [SYSTEM] 系统环境检查通过
[STARTUP-SUCCESS] [21:36:01.487] [系统环境] 组件检查通过 (耗时: 0.001s)
[STARTUP-INFO] [21:36:01.487] [依赖库] 开始检查组件: 依赖库
[STARTUP-SUCCESS] [21:36:01.491] [DEPS] 模块 PyQt6 可用
[STARTUP-SUCCESS] [21:36:01.859] [DEPS] 模块 requests 可用
[STARTUP-SUCCESS] [21:36:01.872] [DEPS] 模块 psutil 可用
[STARTUP-SUCCESS] [21:36:01.875] [DEPS] 模块 sqlite3 可用
[STARTUP-SUCCESS] [21:36:01.973] [DEPS] 模块 cv2 可用
[STARTUP-SUCCESS] [21:36:01.975] [DEPS] 模块 PIL 可用
[STARTUP-SUCCESS] [21:36:01.976] [DEPS] 模块 numpy 可用
[STARTUP-SUCCESS] [21:36:01.981] [DEPS] 模块 win32gui 可用
[STARTUP-SUCCESS] [21:36:01.981] [依赖库] 组件检查通过 (耗时: 0.492s)
[STARTUP-INFO] [21:36:01.982] [配置文件] 开始检查组件: 配置文件
[STARTUP-SUCCESS] [21:36:01.983] [CONFIG] 主配置文件加载成功: 14 项配置
[STARTUP-INFO] [21:36:01.984] [CONFIG] 发现 2 个配置文件
[STARTUP-SUCCESS] [21:36:01.984] [配置文件] 组件检查通过 (耗时: 0.001s)
[STARTUP-INFO] [21:36:01.985] [核心模块] 开始检查组件: 核心模块
[STARTUP-SUCCESS] [21:36:02.005] [CORE] 核心模块 core.simple_config 导入成功
[STARTUP-SUCCESS] [21:36:02.043] [CORE] 核心模块 core.instagram_task 导入成功
[STARTUP-SUCCESS] [21:36:02.065] [CORE] 核心模块 core.leidianapi.LeiDian_Reorganized 导入成功
[STARTUP-SUCCESS] [21:36:02.114] [CORE] 核心模块 core.leidianapi.yijianzhaotu 导入成功
[STARTUP-SUCCESS] [21:36:02.115] [核心模块] 组件检查通过 (耗时: 0.130s)
[STARTUP-INFO] [21:36:02.115] [雷电API] 开始检查组件: 雷电API
[STARTUP-ERROR] [21:36:02.116] [LEIDIAN] 雷电API检查失败: Dnconsole.__init__() missing 2 required positional arguments: 'base_path' and 'share_path'
[STARTUP-ERROR] [21:36:02.116] [雷电API] 组件检查失败 (耗时: 0.000s)
[STARTUP-INFO] [21:36:02.116] [数据库连接] 开始检查组件: 数据库连接
[STARTUP-SUCCESS] [21:36:02.144] [DATABASE] 数据库连接测试通过
[STARTUP-SUCCESS] [21:36:02.145] [数据库连接] 组件检查通过 (耗时: 0.028s)
[STARTUP-INFO] [21:36:02.145] [UI组件] 开始检查组件: UI组件
[STARTUP-INFO] [21:36:02.152] [UI] 未发现现有QApplication实例
[STARTUP-SUCCESS] [21:36:02.152] [UI] UI组件检查通过
[STARTUP-SUCCESS] [21:36:02.153] [UI组件] 组件检查通过 (耗时: 0.008s)
