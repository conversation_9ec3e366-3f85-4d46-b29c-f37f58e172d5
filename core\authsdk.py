# coding=utf-8
import base64
import os
import time
import webbrowser
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from urllib.parse import urlparse

import requests
import wmi
from Crypto.Cipher import PKCS1_v1_5 as PKCS1Cipher
from Crypto.Hash import SHA1
from Crypto.Hash import SHA256
from Crypto.PublicKey import RSA
from Crypto.Signature import PKCS1_v1_5 as PKCS1Signature

"""
本源码采用公认安全系数最高的 RSA 数字签名 与 加密算法
因此后台项目设置签名算法 和 加密类型对应选择 RSA相关选项


首先打开命令提示符安装依赖库：
pip install PyQt6 pycryptodome requests wmi -i https://pypi.tuna.tsinghua.edu.cn/simple

将 authsdk main 两个文件放到项目同一目录
修改【项目ID 34行】【本地版本 35行】【应用私钥 45行】【系统公钥 75行】
脚本调试完毕打包前务必关闭调试模式 42 行
"""


class Auth:
    mode = 0  # 计费模式 该值为 0 时为计时模式，该值为 1 时为计次模式
    appid = '6888589a'  # 项目ID 开发后台对应项目的项目ID
    version = 1  # 本地版本 当后台设置的项目版本大于本地版本时，自动打开下载网址
    update_mode = 1  # 更新模式 该值为 0 时不强制更新，该值为 1 时强制更新（首选）
    change_mode = 0  # 换机模式 该值为 0 时为顶号换机，该值为 1 时为智能换机（首选）
    '''
    【顶号换机】当卡号在第二台设备使用时，不用换机码就可以换机，同时前一台设备自动解绑下线
    【智能换机】强烈推荐，当卡号在第二台设备使用时，需要输入换机码才能换机，用户购买换机码，二次为你带来收益
    '''
    debug = False  # 调试模式 True = 开  False = 关 【注意：打包前务必关闭调试模式】

    # 应用私钥
    private_key_pem = """***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

    # 系统公钥
    public_key_pem = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxk/Cc14Z85MmpmWL8NOc
jnsfemtx7NLgIYNFJw7QZaJyrsPTFwzr1Gocaih/jCZ2sLxL2WICa3kclalfnrj9
ZhNCGt4gC1jwhQsdCw16HTrxF0z2br3sg6/0gpKXR7pM4GTFCRQ6nm+ifYDkMxTf
2KnDQD1cBUcKRAMjdRggGFPRnGLJA+wQd2uRcsz4pthgVOgmRd4A64srK+2A/pyH
IP4TAD0O9h7lp48nO/c9JHJfKFoOh+KzagMB5nmQylLipR5QfTKBrou7HMCe4Gv5
hTQxokNVweFywKTrkRlgtLM3ZiOKFPsWJ4TUpRgODFHdUSMr/22eow4TFM5CUrm6
9QIDAQAB
-----END PUBLIC KEY-----"""

    token = None
    remark = None
    shop = None

    def __init__(self):
        # 🎯 优化：使用默认线路避免初始化时的网络延迟
        self.api = "http://api.kushao.net/v3/"  # 默认使用第一个线路
        self.device_id = self.get_device_id()

    @staticmethod
    def open_url(url):
        webbrowser.open(url)

    @staticmethod
    def is_valid_url(url_string):
        try:
            result = urlparse(url_string)
            return all([result.scheme, result.netloc])
        except ValueError:
            return False

    @staticmethod
    def sha1_digest(data):
        hash_obj = SHA1.new()
        hash_obj.update(data.encode())
        return hash_obj.hexdigest()

    def get_fastest_url(self):
        urls = [
            "http://api.kushao.net/v3/",
            "http://api.kushao.018888.xyz/v3/",
            "http://api.ks.186777.xyz/v3/"
        ]
        # 使用 ThreadPoolExecutor 并行发出请求
        with ThreadPoolExecutor() as executor:
            futures = [executor.submit(self.get_response_time, url) for url in urls]
            fastest_url = None
            fastest_time = float('inf')

            for future in as_completed(futures):
                url, response_time = future.result()
                if response_time < fastest_time:
                    fastest_time = response_time
                    fastest_url = url

        return fastest_url

    @staticmethod
    def get_response_time(url):
        try:
            start_time = time.time()  # 记录开始时间
            response = requests.head(url, timeout=5)  # 发起请求，设置超时时间为5秒
            response_time = time.time() - start_time  # 计算响应时间
            if response.status_code == 200:
                return url, response_time
            else:
                return url, float('inf')  # 如果响应状态不是200，返回无穷大响应时间
        except requests.RequestException as e:
            return url, float('inf')  # 请求异常返回无穷大响应时间

    def post_request(self, api_route, data, sign):
        payload = {
            'data': self.rsa_encrypt(data),
            'sign': sign,
            'encoding': 'utf-8',
        }

        try:
            # 设置请求头
            headers = {'User-Agent': 'Python_' + self.appid,
                       'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'}

            # 发送 POST 请求
            response = requests.post(self.api + api_route, data=payload, headers=headers, timeout=(10, 10))

            # 检查响应状态码
            if response.status_code == 200:
                if self.debug:
                    print('当前api：' + self.api)
                    print('请求参数：' + data)
                    print('加密结果：' + payload['data'])
                    print("返回结果：", response.json())

                return response.json()
            else:
                print(f"POST request failed with status code {response.status_code}")
                print("Response text:", response.text)
                return {'code': response.status_code, 'msg': f'HTTP请求失败，状态码: {response.status_code}'}
        except Exception as e:
            print(f"An error occurred: {e}")
            return {'code': 400, 'msg': '网络请求异常，请更换认证线路尝试'}

    @staticmethod
    def timestamp():
        return str(time.time())[:10]

    def get_device_id(self):
        """    取设备ID    """
        try:
            c = wmi.WMI()
            cpu_info = c.Win32_Processor()
            cpu = cpu_info[0] if cpu_info else wmi.WMI().Win32_Processor().new()

            board_info = c.Win32_BaseBoard()
            baseboard = board_info[0] if board_info else wmi.WMI().Win32_BaseBoard().new()

            components = [
                getattr(cpu, 'Name', ''),
                getattr(cpu, 'ProcessorId', ''),
                getattr(baseboard, 'Manufacturer', ''),
                getattr(baseboard, 'SerialNumber', '')
            ]

            unique_str = ''.join(filter(None, components))
            return self.sha1_digest(unique_str)
        except Exception as e:
            print(f"Error generating device ID: {str(e)}")
            return None

    # RSA公钥加密
    # @param data 待加密数据
    # @param pub_key 公钥
    # @return bytes 加密后数据
    def rsa_encrypt(self, data):
        pub_rsa_key = RSA.importKey(self.public_key_pem)
        cipher = PKCS1Cipher.new(pub_rsa_key)
        encrypted = cipher.encrypt(data.encode())
        return base64.b64encode(encrypted).decode('utf-8')

    # RSA私钥解密
    # @param encrypted 加密数据
    # @param pri_key 私钥
    # @return str 解密后数据
    def rsa_decrypt(self, encrypted):
        pri_rsa_key = RSA.importKey(self.private_key_pem)
        cipher = PKCS1Cipher.new(pri_rsa_key)
        encrypted = base64.b64decode(encrypted)
        decrypted = cipher.decrypt(encrypted, None)
        return decrypted.decode()

    # RSA私钥签名
    # @param data 消息
    # @param pri_key 私钥
    # @return bytes 签名
    def rsa_sign(self, data):
        pri_rsa_key = RSA.importKey(self.private_key_pem)
        signer = PKCS1Signature.new(pri_rsa_key)
        digest = SHA256.new(data.encode('utf8'))
        sign = signer.sign(digest)
        return base64.b64encode(sign).decode('utf-8')

    # RSA公钥验签
    # @param data 消息
    # @param sign 签名
    # @param pub_key 公钥
    # @return bool 验签结果
    def rsa_verify(self, data, sign):
        pub_rsa_key = RSA.importKey(self.public_key_pem)
        verifier = PKCS1Signature.new(pub_rsa_key)
        digest = SHA256.new(data.encode('utf8'))
        decoded_sign = base64.b64decode(sign)
        return verifier.verify(digest, decoded_sign)

    # 得到项目配置信息
    # @param select 取值范围 0-12 不同取值返回不同结果，详见开发文档
    # @return 以字典格式返回相关结果，详见开发文档
    def appinfo(self, select):
        api_route = 'Project/appInfo/' + self.appid
        t = self.timestamp()
        data = '|'.join([str(select), t])
        sign = self.rsa_sign(api_route + str(select) + t)

        return self.post_request(api_route, data, sign)

    # 注册与试用综合接口，不传参数视为获取试用卡号，传入账号密码两个参数视为注册账号
    # @param username 账号 5 - 20 位
    # @param password 密码 8 - 20 位
    def reg_or_try(self, username='', password=''):
        username = username.strip()
        password = password.strip()
        api_route = 'Trial/getCard/' + self.appid
        t = self.timestamp()

        if username == '':
            data = '|'.join([str(self.mode), self.device_id, t])
        else:
            data = '|'.join([str(self.mode), username, password, self.device_id, t])

        sign = self.rsa_sign(api_route + str(self.mode) + username + password + self.device_id + t)

        return self.post_request(api_route, data, sign)

    # 用户换机综合接口
    # @param username 卡号或账号
    # @param password 传入空值视为单卡模式，反之视为账号密码模式
    def change_device(self, username, password=''):
        username = username.strip()
        password = password.strip()

        api_route = 'Device/change/' + self.appid
        t = self.timestamp()

        if self.change_mode == 0:
            result = input('检测到您已换机，确定要更换到当前设备请输入 Y ：')
            if result.upper() == 'Y':
                chgcode = ''
            else:
                return False
        else:
            result = input('检测到您已换机，如需更换到当前设备请输入换机码：')
            chgcode = result.strip()
            if len(chgcode) < 10:
                print('换机码错误')
                return False

        data = '|'.join([str(self.change_mode), username, password, chgcode, self.device_id, t])
        sign = self.rsa_sign(api_route + str(self.change_mode) + username + password + chgcode + self.device_id + t)

        result = self.post_request(api_route, data, sign)
        code = int(self.rsa_decrypt(result['code']))
        if code == 200:
            print('换机成功，请重新运行程序')
        else:
            print(self.rsa_decrypt(result['msg']))

    # 网络验证综合接口，只传入一个参数视为卡号模式，传入两个参数视为账号密码模式
    # @param username 卡号或账号
    # @param password 密码（卡号模式不要传该参数）
    # @return 可根据需要以字典格式返回相关参数，详见开发文档
    def verify(self, username, password=''):
        username = username.strip()
        password = password.strip()

        api_route = 'License/verify/' + self.appid
        t = self.timestamp()
        data = '|'.join([str(self.mode), username, password, self.device_id, t])
        sign = self.rsa_sign(api_route + str(self.mode) + username + password + self.device_id + t)
        result = self.post_request(api_route, data, sign)

        # 检查result是否为None
        if result is None:
            return {'code': 500, 'msg': '网络请求失败，返回结果为空'}

        try:
            code = int(self.rsa_decrypt(result['code']))
        except:
            code = int(result['code'])

        if code == 200:
            t1 = self.rsa_decrypt(result['timeStamp'])
            self.token = self.rsa_decrypt(result['token'])
            self.remark = self.rsa_decrypt(result['remark'])

            if self.mode == 0:
                end_date = self.rsa_decrypt(result['endDate'])
                data = str(code) + end_date + self.token + self.remark + t1
            else:
                surplus_count = self.rsa_decrypt(result['surplusCount'])
                data = str(code) + surplus_count + self.token + self.remark + t1

            if abs(int(t) - int(t1)) > 60:
                return {'code': 502, 'msg': '签名过期'}

            if not self.rsa_verify(data, result['sign']):
                return {'code': 503, 'msg': '签名错误'}

            if self.mode == 0:
                return {
                    'code': code,
                    'end_date': end_date,
                    'msg': '认证成功，有效期到：' + end_date
                }
            else:
                return {
                    'code': code,
                    'surplus_count': surplus_count,
                    'msg': f"认证成功，您还剩余 {surplus_count} 次可用次数"
                }
        else:
            try:
                msg = self.rsa_decrypt(result['msg'])
            except:
                msg = result['msg']

            return {'code': code, 'msg': msg}

    def heart_beat(self):
        if self.token is None:
            print('认证成功才能调用心跳轮询')

        if self.debug:
            cycls = 3
        else:
            cycls = 60
            '''如果想调整心跳间隔，修改该值即可，循环次数不得低于20, 建议 50 - 100 为最佳
            过于频繁发起请求，你的IP将被暂时或永久封禁，心跳轮询过快，如果网络质量不好，还会导致脚本运行时容易中断，因为网络访问总有不确定因素'''

        count = 0
        time.sleep(10)
        api_route = 'License/heartBeat/' + self.appid

        while True:
            t = self.timestamp()
            dt = datetime.fromtimestamp(int(t)).strftime('%H:%M:%S')
            data = '|'.join([self.token, self.device_id, t])
            sign = self.rsa_sign(api_route + self.token + self.device_id + t)
            result = self.post_request(api_route, data, sign)

            try:
                code = int(self.rsa_decrypt(result['code']))
            except:
                code = int(result['code'])

            print(f'心跳状态 {code}，当前心跳间隔 {cycls * 10} 秒  {dt}')
            if code == 200:
                count = 0
                t1 = self.rsa_decrypt(result['timeStamp'])
                if self.mode == 0:
                    end_date = self.rsa_decrypt(result['endDate'])
                    end_time = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')
                    data = "%d%s%s" % (code, end_date, t1)

                else:
                    surplus_count = self.rsa_decrypt(result['surplusCount'])
                    data = "%d%s%s" % (code, surplus_count, t1)

                if abs(int(t) - int(t1)) > 60:
                    print('签名过期')
                    os._exit(0)  # 退出程序

                if not self.rsa_verify(data, result['sign']):
                    print('签名错误')
                    os._exit(0)  # 退出程序

                for i in range(cycls):
                    time.sleep(10)
                    if self.mode == 0:
                        current_time = datetime.fromtimestamp(int(self.timestamp()))
                        if current_time > end_time:
                            print('授权已过期')
                            os._exit(0)  # 退出程序
            else:
                codes_list = [100, 101, 102, 103, 104, 201, 203, 501, 502, 503, 504, 600]
                if code in codes_list:
                    try:
                        print(self.rsa_decrypt(result['msg']))
                    except:
                        print(result['msg'])

                    os._exit(0)  # 退出程序
                else:
                    count += 1
                    if count >= 10:
                        os._exit(0)  # 退出程序
                    else:
                        time.sleep(30)

    # 执行远程函数
    # @param fun_name 函数名称
    # @param params 函数参数，多个参数用英文逗号分隔
    # @return string | boolean 成功返回执行函数的结果，失败返回 False
    def run_function(self, fun_name, params):
        if self.token is None:
            print('认证成功才能调用远程函数')

        api_route = 'Variable/runFunction/' + self.appid
        t = self.timestamp()
        data = '|'.join([fun_name, params, self.token, t])
        sign = self.rsa_sign(api_route + fun_name + params + self.token + t)
        result = self.post_request(api_route, data, sign)

        try:
            code = self.rsa_decrypt(result['code'])
        except Exception as e:
            print(f"An error occurred: {e}")
            code = '400'

        if code == '200':
            t1 = self.rsa_decrypt(result['timeStamp'])
            fun_res = self.rsa_decrypt(result['result'])
            data = code + fun_res + t1

            if abs(int(t) - int(t1)) > 60:
                print('签名过期')
                return False

            if not self.rsa_verify(data, result['sign']):
                print('签名错误')
                return False

            return fun_res
        else:
            print(self.rsa_decrypt(result['msg']))
            return False

    # 读取远程变量
    # @param var_name 变量名称
    # @return string | boolean 成功返回变量值，失败返回 False
    def get_value(self, var_name):
        if self.token is None:
            print('认证成功才能读取远程变量')

        api_route = 'Variable/getVal/' + self.appid
        t = self.timestamp()
        data = '|'.join([var_name, self.token, t])
        sign = self.rsa_sign(api_route + var_name + self.token + t)
        result = self.post_request(api_route, data, sign)

        try:
            code = self.rsa_decrypt(result['code'])
        except Exception as e:
            print(f"An error occurred: {e}")
            code = '400'

        if code == '200':
            t1 = self.rsa_decrypt(result['timeStamp'])
            value = self.rsa_decrypt(result['value'])
            data = code + value + t1

            if abs(int(t) - int(t1)) > 60:
                print('签名过期')
                return False

            if not self.rsa_verify(data, result['sign']):
                print('签名错误')
                return False

            return value
        else:
            print(self.rsa_decrypt(result['msg']))
            return False

    # 更新远程变量
    # @param var_name 变量名称
    # @param value 新变量值
    # @return boolean 成功返回 True，失败返回 False
    def set_value(self, var_name, value):
        api_route = 'Variable/setVal/' + self.appid
        t = self.timestamp()
        data = '|'.join([var_name, value, t])
        sign = self.rsa_sign(api_route + var_name + value + t)
        result = self.post_request(api_route, data, sign)

        try:
            code = self.rsa_decrypt(result['code'])
        except Exception as e:
            print(f"An error occurred: {e}")
            code = '400'

        if code == '200':
            return True
        else:
            print(self.rsa_decrypt(result['msg']))
            return False

    # 充值综合接口，使用新卡对已激活的旧卡或账号进行充值
    # @param auth_code 需要增加时长的卡号或账号(已使用过)
    # @param new_card 用于充值的卡号(未使用过)
    def recharge(self, auth_code, new_card):
        auth_code = auth_code.strip()
        new_card = new_card.strip()

        api_route = 'License/recharge/' + self.appid
        t = self.timestamp()
        data = '|'.join([auth_code, new_card, t])
        sign = self.rsa_sign(api_route + auth_code + new_card + t)
        result = self.post_request(api_route, data, sign)

        try:
            code = self.rsa_decrypt(result['code'])
        except Exception as e:
            print(f"An error occurred: {e}")
            code = '400'

        if code == '200':
            if self.mode == 0:
                duration = self.rsa_decrypt(result['duration'])
                print(f'充值成功，有效时间增加 {duration} 分钟')
            else:
                newCount = self.rsa_decrypt(result['newCount'])
                print(f'充值成功，可用次数增加 {newCount} 次')
        else:
            print(self.rsa_decrypt(result['msg']))

    # 用户修改密码
    # @param username 账号 5 - 20 位
    # @param password 密码 8 - 20 位
    # @param newPassword 新密码 8 - 20 位
    def change_password(self, username, password, new_password):
        username = username.strip()
        password = password.strip()
        new_password = new_password.strip()

        api_route = 'License/changePassword/' + self.appid
        t = self.timestamp()
        data = '|'.join([username, password, new_password, t])
        sign = self.rsa_sign(api_route + username + password + new_password + t)
        result = self.post_request(api_route, data, sign)

        try:
            code = self.rsa_decrypt(result['code'])
        except Exception as e:
            print(f"An error occurred: {e}")
            code = '400'

        if code == '200':
            print('改密成功，新密码：' + new_password)
        else:
            print(self.rsa_decrypt(result['msg']))
