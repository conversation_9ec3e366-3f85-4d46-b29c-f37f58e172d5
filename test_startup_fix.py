#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动问题修复测试脚本
测试在没有配置文件或资源文件的情况下是否会出现NoneType错误
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

def test_config_loading():
    """测试配置加载的健壮性"""
    print("🔍 测试配置加载...")
    
    # 临时改变工作目录，模拟其他电脑环境
    with tempfile.TemporaryDirectory() as temp_dir:
        original_cwd = os.getcwd()
        try:
            os.chdir(temp_dir)
            print(f"  切换到临时目录: {temp_dir}")
            
            # 添加项目路径到sys.path
            project_root = Path(__file__).parent.resolve()
            sys.path.insert(0, str(project_root))
            
            # 测试配置管理器
            from core.simple_config import UnifiedConfigManager
            
            print("  创建配置管理器...")
            config_manager = UnifiedConfigManager()
            
            print("  测试配置访问...")
            emulator_path = config_manager.get("emulator_path", "默认路径")
            print(f"  ✅ 模拟器路径: {emulator_path}")
            
            max_tasks = config_manager.get("max_concurrent_tasks", 1)
            print(f"  ✅ 最大并发任务: {max_tasks}")
            
            monitoring_enabled = config_manager.get("monitoring.task_activity_enabled", False)
            print(f"  ✅ 监控启用: {monitoring_enabled}")
            
            print("  ✅ 配置加载测试通过")
            return True
            
        except Exception as e:
            print(f"  ❌ 配置加载测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            os.chdir(original_cwd)

def test_resource_access():
    """测试资源文件访问"""
    print("\n🔍 测试资源文件访问...")
    
    try:
        # 测试图片目录
        img_dir = Path("img")
        if img_dir.exists():
            files = list(img_dir.glob("*.png"))
            print(f"  ✅ 找到 {len(files)} 个PNG文件")
        else:
            print("  ⚠️ img目录不存在，但程序应该能处理")
        
        # 测试配置文件
        config_file = Path("app_config.json")
        if config_file.exists():
            print("  ✅ 配置文件存在")
        else:
            print("  ⚠️ 配置文件不存在，但程序应该能处理")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 资源访问测试失败: {e}")
        return False

def test_import_modules():
    """测试模块导入"""
    print("\n🔍 测试模块导入...")
    
    try:
        print("  导入配置管理器...")
        from core.simple_config import get_config_manager
        
        print("  导入日志管理器...")
        from core.logger_manager import setup_logging
        
        print("  导入一键找图模块...")
        from core.leidianapi.yijianzhaotu import find_image_position
        
        print("  ✅ 所有模块导入成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 启动问题修复测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_import_modules),
        ("配置加载", test_config_loading),
        ("资源访问", test_resource_access),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        if test_func():
            passed += 1
        
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过！程序应该能在其他电脑上正常启动")
    else:
        print("❌ 部分测试失败，需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
