[11:49:48.078] 🔍 开始显示认证对话框
[11:49:48.078] 🔍 开始创建认证对话框
[11:49:48.079] 🔍 开始初始化SimpleAuthDialog
[11:49:48.080] ✅ QDialog初始化完成
[11:49:48.080] ✅ 基本属性设置完成
[11:49:48.080] 🔍 开始设置UI
[11:49:48.080] ✅ 窗口标题设置完成
[11:49:48.080] ✅ 窗口大小设置完成
[11:49:48.081] ✅ 模态设置完成
[11:49:48.081] ✅ 主布局创建完成
[11:49:48.091] ✅ 标题标签创建完成
[11:49:48.104] ✅ 卡号输入框创建完成
[11:49:48.104] ✅ 线路选择框创建完成
[11:49:48.105] ✅ 记住卡密复选框创建完成
[11:49:48.105] ✅ 公告标签创建完成
[11:49:48.106] ✅ 公告文本框创建完成
[11:49:48.106] ✅ 验证按钮创建完成
[11:49:48.106] ✅ 取消按钮创建完成
[11:49:48.106] ✅ 按钮布局添加完成
[11:49:48.106] ✅ 默认公告设置完成
[11:49:48.106] ✅ UI设置完全成功
[11:49:48.106] ✅ UI设置完成
[11:49:48.107] ✅ 卡密加载完成
[11:49:48.107] ✅ 定时器设置完成
[11:49:48.108] ✅ SimpleAuthDialog初始化完全成功
[11:49:48.108] ✅ 认证对话框创建成功
[11:49:48.108] 🔍 开始显示对话框
[11:49:50.060] 🔍 开始验证认证
[11:49:50.062] ✅ 获取卡号: WpFz17Ov5vJk5GN6
[11:49:50.064] ✅ 按钮状态更新完成
[11:49:50.064] ✅ 获取线路索引: 2
[11:49:50.065] ✅ 准备创建认证线程
[11:49:50.066] ✅ 认证线程已启动
[11:49:50.066] 🔍 开始后台认证: card_number=WpFz17Ov5vJk5GN6, route_index=2
[11:49:50.066] ✅ 定时器已启动
[11:49:50.067] ✅ verify_auth完全成功
[11:49:50.067] 🔍 创建Auth实例...
[11:49:51.564] ✅ Auth实例创建成功: <class 'core.authsdk.Auth'>
[11:49:51.564] 🔍 设置认证路由: http://api.ks.186777.xyz/v3/
[11:49:51.564] 🔍 开始执行认证...
[11:49:51.565] 🔍 等待认证结果...
[11:49:52.628] 🔍 认证结果类型: <class 'dict'>
[11:49:52.629] 🔍 认证结果内容: {'code': 104, 'msg': '已更换设备或超过多开上限'}
[11:49:52.630] ✅ 认证执行完成，准备存储结果
[11:49:52.630] ✅ 认证结果已存储: {'type': 'result', 'data': {'code': 104, 'msg': '已更换设备或超过多开上限'}, 'card_number': 'WpFz17Ov5vJk5GN6'}
