#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 资源路径管理器 - 解决打包后资源文件路径问题
========================================
功能描述: 统一管理资源文件路径，兼容开发环境和打包后的环境
主要方法: get_resource_path(), get_image_path(), get_config_path()
调用关系: 被所有需要访问资源文件的模块调用
注意事项:
- 自动检测是否为打包环境
- 提供多种回退方案确保兼容性
- 支持相对路径和绝对路径
========================================
"""

import os
import sys
from pathlib import Path
from typing import Optional


class ResourceManager:
    """资源路径管理器"""
    
    def __init__(self):
        self._base_path = None
        self._is_packaged = None
        
    @property
    def base_path(self) -> str:
        """获取基础路径"""
        if self._base_path is None:
            self._base_path = self._detect_base_path()
        return self._base_path
    
    @property
    def is_packaged(self) -> bool:
        """检测是否为打包环境"""
        if self._is_packaged is None:
            self._is_packaged = hasattr(sys, '_MEIPASS')
        return self._is_packaged
    
    def _detect_base_path(self) -> str:
        """检测基础路径"""
        if self.is_packaged:
            # PyInstaller打包后的临时目录
            return sys._MEIPASS
        else:
            # 开发环境，使用项目根目录
            # 从core目录向上一级到项目根目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            return os.path.dirname(current_dir)
    
    def get_resource_path(self, relative_path: str) -> str:
        """
        获取资源文件的完整路径
        
        Args:
            relative_path: 相对于项目根目录的路径
            
        Returns:
            完整的资源文件路径
        """
        if os.path.isabs(relative_path):
            return relative_path
        
        return os.path.join(self.base_path, relative_path)
    
    def get_image_path(self, image_name: str) -> str:
        """
        获取图片文件的完整路径
        
        Args:
            image_name: 图片文件名（可以包含子目录）
            
        Returns:
            完整的图片文件路径
        """
        # 如果已经包含img路径，直接使用
        if image_name.startswith('img'):
            return self.get_resource_path(image_name)
        
        # 否则添加img前缀
        return self.get_resource_path(os.path.join('img', image_name))
    
    def get_config_path(self, config_name: str) -> str:
        """
        获取配置文件的完整路径
        
        Args:
            config_name: 配置文件名
            
        Returns:
            完整的配置文件路径
        """
        return self.get_resource_path(config_name)
    
    def exists(self, relative_path: str) -> bool:
        """
        检查资源文件是否存在
        
        Args:
            relative_path: 相对路径
            
        Returns:
            文件是否存在
        """
        full_path = self.get_resource_path(relative_path)
        return os.path.exists(full_path)
    
    def list_images(self) -> list:
        """
        列出所有可用的图片文件
        
        Returns:
            图片文件名列表
        """
        img_dir = self.get_resource_path('img')
        if not os.path.exists(img_dir):
            return []
        
        images = []
        for file in os.listdir(img_dir):
            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif')):
                images.append(file)
        
        return sorted(images)
    
    def get_debug_info(self) -> dict:
        """
        获取调试信息
        
        Returns:
            包含路径信息的字典
        """
        return {
            'is_packaged': self.is_packaged,
            'base_path': self.base_path,
            'img_path': self.get_resource_path('img'),
            'config_path': self.get_resource_path('app_config.json'),
            'img_exists': self.exists('img'),
            'config_exists': self.exists('app_config.json'),
            'available_images': self.list_images()
        }


# 全局资源管理器实例
_resource_manager = None


def get_resource_manager() -> ResourceManager:
    """获取全局资源管理器实例"""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = ResourceManager()
    return _resource_manager


def get_resource_path(relative_path: str) -> str:
    """获取资源文件路径的便捷函数"""
    return get_resource_manager().get_resource_path(relative_path)


def get_image_path(image_name: str) -> str:
    """获取图片文件路径的便捷函数"""
    return get_resource_manager().get_image_path(image_name)


def get_config_path(config_name: str) -> str:
    """获取配置文件路径的便捷函数"""
    return get_resource_manager().get_config_path(config_name)


def resource_exists(relative_path: str) -> bool:
    """检查资源文件是否存在的便捷函数"""
    return get_resource_manager().exists(relative_path)


def print_debug_info():
    """打印调试信息"""
    info = get_resource_manager().get_debug_info()
    print("🎯 资源路径调试信息:")
    print(f"  打包环境: {info['is_packaged']}")
    print(f"  基础路径: {info['base_path']}")
    print(f"  图片目录: {info['img_path']}")
    print(f"  配置文件: {info['config_path']}")
    print(f"  图片目录存在: {info['img_exists']}")
    print(f"  配置文件存在: {info['config_exists']}")
    print(f"  可用图片: {len(info['available_images'])} 个")
    if info['available_images']:
        print(f"    {', '.join(info['available_images'][:5])}")
        if len(info['available_images']) > 5:
            print(f"    ... 还有 {len(info['available_images']) - 5} 个")


if __name__ == "__main__":
    # 测试代码
    print_debug_info()
