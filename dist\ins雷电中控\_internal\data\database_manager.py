#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 数据库管理器 - 基于参考代码2的SQLite实现
========================================
功能描述: 统一的SQLite数据库访问层，支持并发和事务
主要方法: execute_query(), execute_transaction(), sync_with_leidian()
调用关系: 被EmulatorRepository调用，替代JSON缓存
注意事项:
- 使用WAL模式支持并发读写
- 自动创建表结构和索引
- 事务保护确保数据一致性
- 基于参考代码2的同步机制
========================================
"""

import sqlite3

import threading
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import logging

from core.logger_manager import log_info, log_error, log_performance



class DatabaseManager:
    """SQLite数据库管理器 - 基于参考代码2的架构"""

    def __init__(self, db_path: str = "emulator_system.db"):
        """
        初始化数据库管理器

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path).resolve()
        self.conn = None
        self.lock = threading.Lock()
        self.logger = logging.getLogger(self.__class__.__name__)

        try:
            self._init_connection()
            self._create_tables()
            log_info(f"数据库初始化完成: {self.db_path}", component="DatabaseManager")
        except sqlite3.Error as e:
            log_error(f"数据库初始化失败: {e}", component="DatabaseManager")
            raise

    def _init_connection(self):
        """初始化数据库连接，配置WAL模式"""
        self.conn = sqlite3.connect(
            self.db_path,
            check_same_thread=False,
            timeout=30.0
        )
        self.conn.row_factory = self._dict_factory

        # 🎯 配置SQLite优化参数
        self.conn.execute("PRAGMA journal_mode = WAL")  # 支持并发读写
        self.conn.execute("PRAGMA synchronous = NORMAL")  # 平衡性能和安全
        self.conn.execute("PRAGMA cache_size = 10000")  # 增大缓存
        self.conn.execute("PRAGMA temp_store = MEMORY")  # 临时表存储在内存
        self.conn.execute("PRAGMA foreign_keys = ON")  # 启用外键约束

        log_info("SQLite连接已配置WAL模式", component="DatabaseManager")

    def _dict_factory(self, cursor, row):
        """将查询结果转换为字典"""
        return {col[0]: row[idx] for idx, col in enumerate(cursor.description)}

    def _create_tables(self):
        """创建数据库表结构"""
        try:
            with self.conn:
                # 🎯 创建模拟器表 - 基于参考代码2的设计
                # 🎯 检查是否需要数据库迁移（删除旧的运行时字段）
                try:
                    cursor = self.conn.execute("PRAGMA table_info(emulators)")
                    table_info = cursor.fetchall()

                    if table_info:  # 表存在
                        existing_columns = {row[1] for row in table_info}
                        old_fields = {'status', 'pid', 'control_port', 'adb_port', 'last_check'}

                        if old_fields.intersection(existing_columns):
                            log_info("检测到旧表结构，执行数据库迁移", component="DatabaseManager")
                            # 备份数据
                            try:
                                cursor = self.conn.execute("SELECT id, name, path FROM emulators")
                                backup_data = cursor.fetchall()
                            except:
                                backup_data = []

                            # 删除旧表
                            self.conn.execute("DROP TABLE IF EXISTS emulators")
                            log_info("已删除包含运行时字段的旧表", component="DatabaseManager")
                        else:
                            backup_data = []
                    else:
                        # 表不存在，无需迁移
                        backup_data = []

                except Exception as e:
                    log_error(f"检查表结构失败: {e}", component="DatabaseManager")
                    backup_data = []

                # 🎯 创建新表结构：只存储持久化配置，删除运行时状态
                self.conn.execute("""
                    CREATE TABLE IF NOT EXISTS emulators (
                        id INTEGER PRIMARY KEY,
                        name TEXT NOT NULL,
                        path TEXT NOT NULL,
                        -- 用户配置字段（持久化数据）
                        cpu INTEGER DEFAULT 2,
                        memory INTEGER DEFAULT 2048,
                        width INTEGER DEFAULT 540,
                        height INTEGER DEFAULT 960,
                        dpi INTEGER DEFAULT 240,
                        assigned_tasks TEXT DEFAULT '',
                        priority INTEGER DEFAULT 0,
                        notes TEXT DEFAULT '',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 🎯 创建索引优化查询性能（只为有效字段创建索引）
                self.conn.execute("CREATE INDEX IF NOT EXISTS idx_emulator_path ON emulators(path)")
                self.conn.execute("CREATE INDEX IF NOT EXISTS idx_emulator_priority ON emulators(priority)")

                # 🎯 如果有备份数据，恢复基本信息
                if backup_data:
                    for row in backup_data:
                        self.conn.execute("""
                            INSERT OR REPLACE INTO emulators (id, name, path)
                            VALUES (?, ?, ?)
                        """, row)
                    log_info(f"已恢复{len(backup_data)}个模拟器的基本信息", component="DatabaseManager")

                log_info("数据库表结构创建完成", component="DatabaseManager")

        except sqlite3.Error as e:
            log_error(f"创建表结构失败: {e}", component="DatabaseManager")
            raise
    
    def execute_query(self, sql: str, params: Tuple = ()) -> List[Dict[str, Any]]:
        """
        执行查询语句

        Args:
            sql: SQL查询语句
            params: 查询参数

        Returns:
            查询结果列表
        """
        with self.lock:
            try:
                cursor = self.conn.execute(sql, params)
                results = cursor.fetchall()
                log_performance(f"查询执行完成: {len(results)}行", component="DatabaseManager")
                return results
            except sqlite3.Error as e:
                log_error(f"查询执行失败: {sql}, 错误: {e}", component="DatabaseManager")
                raise
    
    def execute_transaction(self, operations: List[Tuple[str, Tuple]]) -> bool:
        """
        执行事务操作

        Args:
            operations: 操作列表，每个元素为(sql, params)

        Returns:
            操作是否成功
        """
        with self.lock:
            try:
                with self.conn:
                    for sql, params in operations:
                        self.conn.execute(sql, params)
                log_info(f"事务执行成功: {len(operations)}个操作", component="DatabaseManager")
                return True
            except sqlite3.Error as e:
                log_error(f"事务执行失败: {e}", component="DatabaseManager")
                return False
    
    def sync_with_leidian(self, emulators_from_console: List[Dict[str, Any]]) -> bool:
        """
        与雷电控制台同步模拟器数据 - 基于参考代码2的同步机制
        
        Args:
            emulators_from_console: 从控制台获取的模拟器列表
            
        Returns:
            同步是否成功
        """
        try:
            # 🎯 获取现有数据库中的模拟器
            existing_emulators = {row['id']: row for row in self.execute_query("SELECT * FROM emulators")}
            
            operations = []
            current_time = datetime.now().isoformat()
            
            # 🎯 处理控制台中的模拟器
            console_ids = set()
            for emulator_data in emulators_from_console:
                emulator_id = emulator_data['id']
                console_ids.add(emulator_id)
                
                if emulator_id in existing_emulators:
                    # 更新现有模拟器
                    operations.append((
                        "UPDATE emulators SET name=?, path=?, updated_at=? WHERE id=?",
                        (emulator_data['name'], emulator_data['path'], current_time, emulator_id)
                    ))
                else:
                    # 插入新模拟器
                    operations.append((
                        "INSERT INTO emulators (id, name, path, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
                        (emulator_id, emulator_data['name'], emulator_data['path'], current_time, current_time)
                    ))
            
            # 🎯 删除控制台中不存在的模拟器
            for existing_id in existing_emulators:
                if existing_id not in console_ids:
                    operations.append((
                        "DELETE FROM emulators WHERE id=?",
                        (existing_id,)
                    ))
            
            # 执行同步操作
            success = self.execute_transaction(operations)
            if success:
                log_info(f"模拟器数据同步完成: {len(console_ids)}个模拟器", component="DatabaseManager")
            
            return success
            
        except Exception as e:
            log_error(f"模拟器数据同步失败: {e}", component="DatabaseManager")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            log_info("数据库连接已关闭", component="DatabaseManager")


# 🎯 全局数据库管理器实例
_db_manager = None

def get_database_manager() -> DatabaseManager:
    """获取全局数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager
