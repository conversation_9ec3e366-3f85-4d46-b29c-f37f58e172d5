#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证网络连接测试工具
用于诊断HTTP 514错误的原因
"""

import requests
import time
from core.authsdk import Auth

def test_network_connectivity():
    """测试网络连接"""
    print("🔍 开始网络连接测试...")
    
    # 测试基本网络连接
    test_urls = [
        "https://www.baidu.com",
        "https://www.google.com", 
        "http://api.kushao.net/v3/",
        "http://api.kushao.018888.xyz/v3/",
        "http://api.ks.186777.xyz/v3/"
    ]
    
    for url in test_urls:
        try:
            print(f"\n📡 测试连接: {url}")
            response = requests.get(url, timeout=10)
            print(f"✅ 状态码: {response.status_code}")
            print(f"✅ 响应时间: {response.elapsed.total_seconds():.2f}秒")
        except Exception as e:
            print(f"❌ 连接失败: {e}")

def test_auth_request():
    """测试认证请求"""
    print("\n🔍 开始认证请求测试...")
    
    try:
        auth = Auth()
        print(f"✅ Auth实例创建成功")
        print(f"📍 默认API: {auth.api}")
        print(f"📱 设备ID: {auth.device_id}")
        
        # 测试不同的认证线路
        routes = [
            "http://api.kushao.net/v3/",
            "http://api.kushao.018888.xyz/v3/", 
            "http://api.ks.186777.xyz/v3/"
        ]
        
        card_number = "WpFz17Ov5vJk5GN6"
        
        for i, route in enumerate(routes):
            print(f"\n🔄 测试线路 {i+1}: {route}")
            auth.api = route
            
            try:
                result = auth.verify(card_number)
                print(f"✅ 认证结果: {result}")
            except Exception as e:
                print(f"❌ 认证失败: {e}")
                
    except Exception as e:
        print(f"❌ Auth实例创建失败: {e}")

def test_manual_request():
    """手动测试HTTP请求"""
    print("\n🔍 开始手动HTTP请求测试...")
    
    url = "http://api.kushao.net/v3/"
    
    try:
        # 测试简单的GET请求
        print(f"📡 GET请求: {url}")
        response = requests.get(url, timeout=10)
        print(f"✅ GET状态码: {response.status_code}")
        print(f"✅ GET响应: {response.text[:200]}...")
        
        # 测试POST请求（模拟认证请求）
        print(f"\n📡 POST请求: {url}License/verify/6888589a")
        headers = {
            'User-Agent': 'Python_6888589a',
            'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
        }
        data = {'data': 'test_data'}
        
        response = requests.post(url + "License/verify/6888589a", 
                               data=data, headers=headers, timeout=10)
        print(f"✅ POST状态码: {response.status_code}")
        print(f"✅ POST响应: {response.text[:200]}...")
        
    except Exception as e:
        print(f"❌ 手动请求失败: {e}")

if __name__ == "__main__":
    print("🚀 认证网络诊断工具")
    print("=" * 50)
    
    test_network_connectivity()
    test_manual_request() 
    test_auth_request()
    
    print("\n" + "=" * 50)
    print("🎯 诊断完成")
