import win32gui
import win32ui
import win32con
import cv2
import numpy as np
from PIL import Image
import os
import time
import ctypes
from ctypes import wintypes

def find_image_position(template_path="ins.png", window_title="雷电模拟器-1"):
    """
    一键获取图片在雷电模拟器中的位置
    
    参数:
        template_path: 模板图片路径 (默认: "ins.png")
        window_title: 窗口标题 (默认: "雷电模拟器-1")
    
    返回:
        (x, y) 坐标元组，如果未找到返回 None
    
    使用示例:
        position = find_image_position()
        if position:
            x, y = position
            print(f"位置: ({x}, {y})")
    """
    
    # 1. 查找窗口
    def find_window():
        # 找主窗口
        main_hwnd = None
        def enum_main(hwnd, windows):
            try:
                if win32gui.IsWindowVisible(hwnd):
                    text = win32gui.GetWindowText(hwnd)
                    if window_title in text:
                        windows.append(hwnd)
            except:
                pass
            return True

        main_windows = []
        win32gui.EnumWindows(enum_main, main_windows)
        if main_windows:
            main_hwnd = main_windows[0]
        
        # 找渲染窗口
        def enum_render(hwnd, render_windows):
            try:
                text = win32gui.GetWindowText(hwnd)
                cls = win32gui.GetClassName(hwnd)
                
                if (text == 'TheRender' and cls == 'RenderWindow') or cls == 'RenderWindow':
                    rect = win32gui.GetWindowRect(hwnd)
                    w, h = rect[2] - rect[0], rect[3] - rect[1]
                    if w > 200 and h > 300:
                        render_windows.append((hwnd, w * h))
            except:
                pass
            return True

        render_windows = []
        win32gui.EnumWindows(enum_render, render_windows)
        
        # 找子窗口
        if main_hwnd:
            def enum_child(hwnd, child_windows):
                try:
                    cls = win32gui.GetClassName(hwnd)
                    if cls in ['RenderWindow', 'subWin']:
                        rect = win32gui.GetWindowRect(hwnd)
                        w, h = rect[2] - rect[0], rect[3] - rect[1]
                        if w > 200 and h > 300:
                            child_windows.append((hwnd, w * h))
                except:
                    pass
                return True

            child_windows = []
            win32gui.EnumChildWindows(main_hwnd, enum_child, child_windows)
            render_windows.extend(child_windows)
        
        return max(render_windows, key=lambda x: x[1])[0] if render_windows else None
    
    # 2. 截图
    def screenshot(hwnd):
        if not hwnd:
            return None

        rect = win32gui.GetClientRect(hwnd)
        w, h = rect[2], rect[3]

        hwndDC = win32gui.GetDC(hwnd)
        mfcDC = win32ui.CreateDCFromHandle(hwndDC)
        saveDC = mfcDC.CreateCompatibleDC()
        saveBitMap = win32ui.CreateBitmap()
        saveBitMap.CreateCompatibleBitmap(mfcDC, w, h)
        saveDC.SelectObject(saveBitMap)

        user32 = ctypes.windll.user32
        user32.PrintWindow.argtypes = [wintypes.HWND, wintypes.HDC, wintypes.UINT]
        user32.PrintWindow.restype = wintypes.BOOL
        
        result = user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 2)
        if not result:
            saveDC.BitBlt((0, 0), (w, h), mfcDC, (0, 0), win32con.SRCCOPY)

        bmpinfo = saveBitMap.GetInfo()
        bmpstr = saveBitMap.GetBitmapBits(True)
        img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']), 
                              bmpstr, 'raw', 'BGRX', 0, 1)
        img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

        win32gui.DeleteObject(saveBitMap.GetHandle())
        saveDC.DeleteDC()
        mfcDC.DeleteDC()
        win32gui.ReleaseDC(hwnd, hwndDC)
        return img_cv
    
    # 3. 查找图片
    def find_in_image(screenshot, template):
        best_result = None
        best_confidence = 0

        # 优化缩放顺序：以1.0为中心，常用尺寸优先
        scales = [1.0, 0.9, 1.1, 0.8, 1.2, 0.7, 1.3, 0.6, 1.4, 0.5, 1.5, 0.4, 1.6, 0.3, 0.7, 0.2, 1.8, 0.1, 1.9]
        
        for scale in scales:
            th, tw = template.shape[:2]
            nw, nh = int(tw * scale), int(th * scale)
            
            if nw < 5 or nh < 5 or nw > screenshot.shape[1] or nh > screenshot.shape[0]:
                continue
                
            scaled = cv2.resize(template, (nw, nh))
            result = cv2.matchTemplate(screenshot, scaled, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)
            
            if max_val > best_confidence:
                best_confidence = max_val
                x, y = max_loc
                best_result = (x + nw // 2, y + nh // 2, max_val)

                # 早期退出优化：如果找到完美匹配，直接返回
                if max_val >= 0.95:
                    break
        #≥ 0.8 (80%): 高质量匹配，可信度高 ✅      0.5-0.8: 中等匹配，可能误匹配 ⚠️     
        return best_result if best_confidence >= 0.75 else None
    
    # 执行查找
    start_time = time.time()

    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # 如果template_path不是绝对路径，则相对于脚本目录
    if not os.path.isabs(template_path):
        template_path = os.path.join(script_dir, template_path)

    if not os.path.exists(template_path):
        return None

    template = cv2.imread(template_path)
    if template is None:
        return None

    hwnd = find_window()
    if not hwnd:
        return None

    img = screenshot(hwnd)
    if img is None:
        return None

    result = find_in_image(img, template)

    end_time = time.time()
    elapsed_time = end_time - start_time

    if result:
        x, y, confidence = result
        return (x, y, elapsed_time, confidence)

    return None

# 使用示例
if __name__ == "__main__":
    result = find_image_position()

    if result:
        if len(result) == 4:  # 包含耗时和置信度信息
            x, y, elapsed_time, confidence = result
            print(f"位置: ({x}, {y}), 耗时: {elapsed_time:.3f}秒, 置信度: {confidence:.3f}")
        elif len(result) == 3:  # 包含耗时信息
            x, y, elapsed_time = result
            print(f"位置: ({x}, {y}), 耗时: {elapsed_time:.3f}秒")
        else:  # 兼容旧版本返回格式
            x, y = result
            print(f"位置: ({x}, {y})")
    else:
        print("未找到")
