(['e:\\VScodexiangmu\\insleidian2\\main.py'],
 ['e:\\VScodexiangmu\\insleidian2'],
 ['PyQt6.QtCore',
  'PyQt6.QtGui',
  'PyQt6.QtWidgets',
  'requests',
  'psutil',
  'sqlite3'],
 [('E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('E:\\python-3.12.9\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('E:\\python-3.12.9\\Lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('app_config.json',
   'e:\\VScodexiangmu\\insleidian2\\app_config.json',
   'DATA'),
  ('config\\test_instagram_dm.json',
   'e:\\VScodexiangmu\\insleidian2\\config\\test_instagram_dm.json',
   'DATA'),
  ('config\\test_recall_config.json',
   'e:\\VScodexiangmu\\insleidian2\\config\\test_recall_config.json',
   'DATA'),
  ('data\\__init__.py',
   'e:\\VScodexiangmu\\insleidian2\\data\\__init__.py',
   'DATA'),
  ('data\\__pycache__\\__init__.cpython-312.pyc',
   'e:\\VScodexiangmu\\insleidian2\\data\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('data\\__pycache__\\database_manager.cpython-312.pyc',
   'e:\\VScodexiangmu\\insleidian2\\data\\__pycache__\\database_manager.cpython-312.pyc',
   'DATA'),
  ('data\\database_manager.py',
   'e:\\VScodexiangmu\\insleidian2\\data\\database_manager.py',
   'DATA'),
  ('data\\models\\__init__.py',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\__init__.py',
   'DATA'),
  ('data\\models\\__pycache__\\__init__.cpython-312.pyc',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('data\\models\\__pycache__\\emulator_model.cpython-312.pyc',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\__pycache__\\emulator_model.cpython-312.pyc',
   'DATA'),
  ('data\\models\\emulator_model.py',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\emulator_model.py',
   'DATA'),
  ('data\\repositories\\__init__.py',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\__init__.py',
   'DATA'),
  ('data\\repositories\\__pycache__\\__init__.cpython-312.pyc',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('data\\repositories\\__pycache__\\emulator_repository.cpython-312.pyc',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\__pycache__\\emulator_repository.cpython-312.pyc',
   'DATA'),
  ('data\\repositories\\emulator_repository.py',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\emulator_repository.py',
   'DATA'),
  ('img\\V2.png', 'e:\\VScodexiangmu\\insleidian2\\img\\V2.png', 'DATA'),
  ('img\\chehui.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\chehui.png',
   'DATA'),
  ('img\\chehui2.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\chehui2.png',
   'DATA'),
  ('img\\douyin.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\douyin.png',
   'DATA'),
  ('img\\faxiaoxi.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\faxiaoxi.png',
   'DATA'),
  ('img\\ins.png', 'e:\\VScodexiangmu\\insleidian2\\img\\ins.png', 'DATA')],
 '3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'E:\\python-3.12.9\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'E:\\python-3.12.9\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('main', 'E:\\VScodexiangmu\\insleidian2\\main.py', 'PYSOURCE')],
 [('_distutils_hack',
   'E:\\python-3.12.9\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('importlib.util', 'E:\\python-3.12.9\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\python-3.12.9\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'E:\\python-3.12.9\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'E:\\python-3.12.9\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'E:\\python-3.12.9\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'E:\\python-3.12.9\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'E:\\python-3.12.9\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'E:\\python-3.12.9\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'E:\\python-3.12.9\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'E:\\python-3.12.9\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'E:\\python-3.12.9\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators',
   'E:\\python-3.12.9\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'E:\\python-3.12.9\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('copy', 'E:\\python-3.12.9\\Lib\\copy.py', 'PYMODULE'),
  ('random', 'E:\\python-3.12.9\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'E:\\python-3.12.9\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'E:\\python-3.12.9\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'E:\\python-3.12.9\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'E:\\python-3.12.9\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'E:\\python-3.12.9\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'E:\\python-3.12.9\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'E:\\python-3.12.9\\Lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'E:\\python-3.12.9\\Lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'E:\\python-3.12.9\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'E:\\python-3.12.9\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'E:\\python-3.12.9\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'E:\\python-3.12.9\\Lib\\gettext.py', 'PYMODULE'),
  ('struct', 'E:\\python-3.12.9\\Lib\\struct.py', 'PYMODULE'),
  ('email.charset', 'E:\\python-3.12.9\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'E:\\python-3.12.9\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime',
   'E:\\python-3.12.9\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'E:\\python-3.12.9\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'E:\\python-3.12.9\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'E:\\python-3.12.9\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'E:\\python-3.12.9\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr',
   'E:\\python-3.12.9\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'E:\\python-3.12.9\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse', 'E:\\python-3.12.9\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'E:\\python-3.12.9\\Lib\\ipaddress.py', 'PYMODULE'),
  ('socket', 'E:\\python-3.12.9\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'E:\\python-3.12.9\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'E:\\python-3.12.9\\Lib\\quopri.py', 'PYMODULE'),
  ('inspect', 'E:\\python-3.12.9\\Lib\\inspect.py', 'PYMODULE'),
  ('token', 'E:\\python-3.12.9\\Lib\\token.py', 'PYMODULE'),
  ('importlib.machinery',
   'E:\\python-3.12.9\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis', 'E:\\python-3.12.9\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'E:\\python-3.12.9\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'E:\\python-3.12.9\\Lib\\ast.py', 'PYMODULE'),
  ('contextlib', 'E:\\python-3.12.9\\Lib\\contextlib.py', 'PYMODULE'),
  ('textwrap', 'E:\\python-3.12.9\\Lib\\textwrap.py', 'PYMODULE'),
  ('zipfile', 'E:\\python-3.12.9\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'E:\\python-3.12.9\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'E:\\python-3.12.9\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'E:\\python-3.12.9\\Lib\\py_compile.py', 'PYMODULE'),
  ('lzma', 'E:\\python-3.12.9\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression', 'E:\\python-3.12.9\\Lib\\_compression.py', 'PYMODULE'),
  ('bz2', 'E:\\python-3.12.9\\Lib\\bz2.py', 'PYMODULE'),
  ('shutil', 'E:\\python-3.12.9\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'E:\\python-3.12.9\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'E:\\python-3.12.9\\Lib\\gzip.py', 'PYMODULE'),
  ('fnmatch', 'E:\\python-3.12.9\\Lib\\fnmatch.py', 'PYMODULE'),
  ('email', 'E:\\python-3.12.9\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'E:\\python-3.12.9\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser',
   'E:\\python-3.12.9\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'E:\\python-3.12.9\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'E:\\python-3.12.9\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'E:\\python-3.12.9\\Lib\\tempfile.py', 'PYMODULE'),
  ('tokenize', 'E:\\python-3.12.9\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\python-3.12.9\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc', 'E:\\python-3.12.9\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.abc', 'E:\\python-3.12.9\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib', 'E:\\python-3.12.9\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('setuptools',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('typing_extensions',
   'E:\\python-3.12.9\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'E:\\python-3.12.9\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio', 'E:\\python-3.12.9\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'E:\\python-3.12.9\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('subprocess', 'E:\\python-3.12.9\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'E:\\python-3.12.9\\Lib\\signal.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'E:\\python-3.12.9\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl', 'E:\\python-3.12.9\\Lib\\ssl.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'E:\\python-3.12.9\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'E:\\python-3.12.9\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'E:\\python-3.12.9\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'E:\\python-3.12.9\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues', 'E:\\python-3.12.9\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners',
   'E:\\python-3.12.9\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock', 'E:\\python-3.12.9\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   'E:\\python-3.12.9\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'E:\\python-3.12.9\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks', 'E:\\python-3.12.9\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', 'E:\\python-3.12.9\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'E:\\python-3.12.9\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto',
   'E:\\python-3.12.9\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'E:\\python-3.12.9\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'E:\\python-3.12.9\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'E:\\python-3.12.9\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'E:\\python-3.12.9\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events', 'E:\\python-3.12.9\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'E:\\python-3.12.9\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'E:\\python-3.12.9\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock', 'E:\\python-3.12.9\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'E:\\python-3.12.9\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'E:\\python-3.12.9\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'E:\\python-3.12.9\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main', 'E:\\python-3.12.9\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner',
   'E:\\python-3.12.9\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'E:\\python-3.12.9\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite', 'E:\\python-3.12.9\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'E:\\python-3.12.9\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'E:\\python-3.12.9\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'E:\\python-3.12.9\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result',
   'E:\\python-3.12.9\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util', 'E:\\python-3.12.9\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('pkgutil', 'E:\\python-3.12.9\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'E:\\python-3.12.9\\Lib\\zipimport.py', 'PYMODULE'),
  ('pprint', 'E:\\python-3.12.9\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'E:\\python-3.12.9\\Lib\\dataclasses.py', 'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('sysconfig', 'E:\\python-3.12.9\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'E:\\python-3.12.9\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._distutils',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('queue', 'E:\\python-3.12.9\\Lib\\queue.py', 'PYMODULE'),
  ('platform', 'E:\\python-3.12.9\\Lib\\platform.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'E:\\python-3.12.9\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter', 'E:\\python-3.12.9\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'E:\\python-3.12.9\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'E:\\python-3.12.9\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'E:\\python-3.12.9\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'E:\\python-3.12.9\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'E:\\python-3.12.9\\Lib\\http\\server.py', 'PYMODULE'),
  ('http', 'E:\\python-3.12.9\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('socketserver', 'E:\\python-3.12.9\\Lib\\socketserver.py', 'PYMODULE'),
  ('mimetypes', 'E:\\python-3.12.9\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.client', 'E:\\python-3.12.9\\Lib\\http\\client.py', 'PYMODULE'),
  ('html', 'E:\\python-3.12.9\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'E:\\python-3.12.9\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'E:\\python-3.12.9\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data', 'E:\\python-3.12.9\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('tty', 'E:\\python-3.12.9\\Lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser', 'E:\\python-3.12.9\\Lib\\configparser.py', 'PYMODULE'),
  ('packaging.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes', 'E:\\python-3.12.9\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'E:\\python-3.12.9\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian', 'E:\\python-3.12.9\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('packaging.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib.request', 'E:\\python-3.12.9\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'E:\\python-3.12.9\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'E:\\python-3.12.9\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'E:\\python-3.12.9\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'E:\\python-3.12.9\\Lib\\netrc.py', 'PYMODULE'),
  ('http.cookiejar', 'E:\\python-3.12.9\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('urllib.response',
   'E:\\python-3.12.9\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error', 'E:\\python-3.12.9\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('setuptools.glob',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'E:\\python-3.12.9\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser',
   'E:\\python-3.12.9\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types', 'E:\\python-3.12.9\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'E:\\python-3.12.9\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('glob', 'E:\\python-3.12.9\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools._shutil',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'E:\\python-3.12.9\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('__future__', 'E:\\python-3.12.9\\Lib\\__future__.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'E:\\python-3.12.9\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'E:\\python-3.12.9\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'E:\\python-3.12.9\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'E:\\python-3.12.9\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'E:\\python-3.12.9\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax', 'E:\\python-3.12.9\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('hmac', 'E:\\python-3.12.9\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'E:\\python-3.12.9\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('pickle', 'E:\\python-3.12.9\\Lib\\pickle.py', 'PYMODULE'),
  ('_compat_pickle', 'E:\\python-3.12.9\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('multiprocessing.process',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'E:\\python-3.12.9\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('sqlite3', 'E:\\python-3.12.9\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump', 'E:\\python-3.12.9\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('sqlite3.__main__',
   'E:\\python-3.12.9\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('code', 'E:\\python-3.12.9\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'E:\\python-3.12.9\\Lib\\codeop.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'E:\\python-3.12.9\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('psutil',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('requests',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies', 'E:\\python-3.12.9\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('requests.models',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks', 'E:\\python-3.12.9\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'E:\\python-3.12.9\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('cryptography',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('PyQt6',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('_py_abc', 'E:\\python-3.12.9\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'E:\\python-3.12.9\\Lib\\stringprep.py', 'PYMODULE'),
  ('concurrent.futures',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'E:\\python-3.12.9\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('threading', 'E:\\python-3.12.9\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'E:\\python-3.12.9\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('core.authsdk',
   'e:\\VScodexiangmu\\insleidian2\\core\\authsdk.py',
   'PYMODULE'),
  ('core', '-', 'PYMODULE'),
  ('Crypto.Signature.PKCS1_v1_5',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Signature\\PKCS1_v1_5.py',
   'PYMODULE'),
  ('Crypto.Signature.pkcs1_15',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Signature\\pkcs1_15.py',
   'PYMODULE'),
  ('Crypto.Util.asn1',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\asn1.py',
   'PYMODULE'),
  ('Crypto.Util',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util._cpu_features',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE'),
  ('Crypto.Util._raw_api',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE'),
  ('ctypes.util', 'E:\\python-3.12.9\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'E:\\python-3.12.9\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'E:\\python-3.12.9\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'E:\\python-3.12.9\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'E:\\python-3.12.9\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'E:\\python-3.12.9\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('cffi',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('Crypto.Util._file_system',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE'),
  ('Crypto',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util.py3compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE'),
  ('Crypto.Util.number',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE'),
  ('Crypto.Random',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE'),
  ('Crypto.Signature',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Signature\\__init__.py',
   'PYMODULE'),
  ('Crypto.PublicKey.RSA',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\PublicKey\\RSA.py',
   'PYMODULE'),
  ('Crypto.PublicKey._openssh',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\PublicKey\\_openssh.py',
   'PYMODULE'),
  ('Crypto.Util.strxor',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE'),
  ('Crypto.Protocol.KDF',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE'),
  ('Crypto.Protocol',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher._EKSBlowfish',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE'),
  ('Crypto.Hash.BLAKE2s',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE'),
  ('Crypto.Hash.CMAC',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.HMAC',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.MD5',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA512',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE'),
  ('Crypto.Cipher.AES',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE'),
  ('Crypto.IO.PEM',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\IO\\PEM.py',
   'PYMODULE'),
  ('Crypto.Cipher.DES3',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\DES3.py',
   'PYMODULE'),
  ('Crypto.Cipher.DES',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\DES.py',
   'PYMODULE'),
  ('Crypto.Util.Padding',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\Padding.py',
   'PYMODULE'),
  ('Crypto.IO.PKCS8',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\IO\\PKCS8.py',
   'PYMODULE'),
  ('Crypto.IO._PBES',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\IO\\_PBES.py',
   'PYMODULE'),
  ('Crypto.Cipher.ARC2',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\ARC2.py',
   'PYMODULE'),
  ('Crypto.IO',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\IO\\__init__.py',
   'PYMODULE'),
  ('Crypto.Math.Primality',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\Primality.py',
   'PYMODULE'),
  ('Crypto.Math',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\__init__.py',
   'PYMODULE'),
  ('Crypto.Math.Numbers',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\Numbers.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerNative',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\_IntegerNative.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerBase',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\_IntegerBase.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerCustom',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\_IntegerCustom.py',
   'PYMODULE'),
  ('Crypto.Random.random',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Random\\random.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerGMP',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\_IntegerGMP.py',
   'PYMODULE'),
  ('Crypto.PublicKey',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\PublicKey\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA256',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA1',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE'),
  ('Crypto.Hash',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_512',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE'),
  ('Crypto.Hash.keccak',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_384',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_256',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_224',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA384',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA224',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE'),
  ('Crypto.Cipher.PKCS1_v1_5',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\PKCS1_v1_5.py',
   'PYMODULE'),
  ('Crypto.Cipher._pkcs1_oaep_decode',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_pkcs1_oaep_decode.py',
   'PYMODULE'),
  ('Crypto.Cipher',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_kwp',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_kwp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_kw',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_kw.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ocb',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_gcm',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_siv',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ccm',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_eax',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_openpgp',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ctr',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ofb',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cfb',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cbc',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ecb',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE'),
  ('wmi', 'E:\\python-3.12.9\\Lib\\site-packages\\wmi.py', 'PYMODULE'),
  ('pywintypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('win32com.client',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('winerror',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('win32con',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.client.build',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.universal',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('pythoncom',
   'E:\\python-3.12.9\\Lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('_strptime', 'E:\\python-3.12.9\\Lib\\_strptime.py', 'PYMODULE'),
  ('json', 'E:\\python-3.12.9\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'E:\\python-3.12.9\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'E:\\python-3.12.9\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'E:\\python-3.12.9\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('ui.main_window_v2',
   'e:\\VScodexiangmu\\insleidian2\\ui\\main_window_v2.py',
   'PYMODULE'),
  ('ui', 'e:\\VScodexiangmu\\insleidian2\\ui\\__init__.py', 'PYMODULE'),
  ('ui.instagram_follow_ui',
   'e:\\VScodexiangmu\\insleidian2\\ui\\instagram_follow_ui.py',
   'PYMODULE'),
  ('ui.style_manager',
   'e:\\VScodexiangmu\\insleidian2\\ui\\style_manager.py',
   'PYMODULE'),
  ('ui.instagram_dm_ui',
   'e:\\VScodexiangmu\\insleidian2\\ui\\instagram_dm_ui.py',
   'PYMODULE'),
  ('ui.basic_config_ui',
   'e:\\VScodexiangmu\\insleidian2\\ui\\basic_config_ui.py',
   'PYMODULE'),
  ('ui.settings_ui',
   'e:\\VScodexiangmu\\insleidian2\\ui\\settings_ui.py',
   'PYMODULE'),
  ('core.heartbeat_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\heartbeat_manager.py',
   'PYMODULE'),
  ('core.screenshot_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\screenshot_manager.py',
   'PYMODULE'),
  ('core.native',
   'e:\\VScodexiangmu\\insleidian2\\core\\native\\__init__.py',
   'PYMODULE'),
  ('core.native.screenshot_engine',
   'e:\\VScodexiangmu\\insleidian2\\core\\native\\screenshot_engine.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'E:\\python-3.12.9\\Lib\\colorsys.py', 'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL._typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('numpy.typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.strings',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.rec',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.f2py',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput', 'E:\\python-3.12.9\\Lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py.symbolic',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest', 'E:\\python-3.12.9\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'E:\\python-3.12.9\\Lib\\pdb.py', 'PYMODULE'),
  ('bdb', 'E:\\python-3.12.9\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'E:\\python-3.12.9\\Lib\\cmd.py', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._typing._ufunc',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('yaml',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.composer',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.events',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.tokens',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yaml.error',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('PIL._util',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.Image',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree', 'E:\\python-3.12.9\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('PIL.ImageMode',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('core.native.base_api',
   'e:\\VScodexiangmu\\insleidian2\\core\\native\\base_api.py',
   'PYMODULE'),
  ('core.native.image_recognition_engine',
   'e:\\VScodexiangmu\\insleidian2\\core\\native\\image_recognition_engine.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('core.unified_emulator_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\unified_emulator_manager.py',
   'PYMODULE'),
  ('data.models.emulator_model',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\emulator_model.py',
   'PYMODULE'),
  ('data.models',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\__init__.py',
   'PYMODULE'),
  ('data', 'e:\\VScodexiangmu\\insleidian2\\data\\__init__.py', 'PYMODULE'),
  ('data.repositories.emulator_repository',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\emulator_repository.py',
   'PYMODULE'),
  ('data.repositories',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\__init__.py',
   'PYMODULE'),
  ('data.database_manager',
   'e:\\VScodexiangmu\\insleidian2\\data\\database_manager.py',
   'PYMODULE'),
  ('core.status_converter',
   'e:\\VScodexiangmu\\insleidian2\\core\\status_converter.py',
   'PYMODULE'),
  ('ui.styled_widgets',
   'e:\\VScodexiangmu\\insleidian2\\ui\\styled_widgets.py',
   'PYMODULE'),
  ('ui.ui_service_layer',
   'e:\\VScodexiangmu\\insleidian2\\ui\\ui_service_layer.py',
   'PYMODULE'),
  ('core.async_bridge',
   'e:\\VScodexiangmu\\insleidian2\\core\\async_bridge.py',
   'PYMODULE'),
  ('core.instagram_follow_task',
   'e:\\VScodexiangmu\\insleidian2\\core\\instagram_follow_task.py',
   'PYMODULE'),
  ('core.instagram_task',
   'e:\\VScodexiangmu\\insleidian2\\core\\instagram_task.py',
   'PYMODULE'),
  ('core.leidianapi.yijianzhaotu',
   'e:\\VScodexiangmu\\insleidian2\\core\\leidianapi\\yijianzhaotu.py',
   'PYMODULE'),
  ('core.leidianapi', '-', 'PYMODULE'),
  ('aiofiles',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\__init__.py',
   'PYMODULE'),
  ('aiofiles.tempfile',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\tempfile\\__init__.py',
   'PYMODULE'),
  ('aiofiles.tempfile.temptypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\tempfile\\temptypes.py',
   'PYMODULE'),
  ('aiofiles.threadpool.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\utils.py',
   'PYMODULE'),
  ('aiofiles.threadpool.text',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\text.py',
   'PYMODULE'),
  ('aiofiles.threadpool.binary',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\binary.py',
   'PYMODULE'),
  ('aiofiles.base',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\base.py',
   'PYMODULE'),
  ('aiofiles.threadpool',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\__init__.py',
   'PYMODULE'),
  ('core.leidianapi.LeiDian_Reorganized',
   'e:\\VScodexiangmu\\insleidian2\\core\\leidianapi\\LeiDian_Reorganized.py',
   'PYMODULE'),
  ('core.config_hot_reload',
   'e:\\VScodexiangmu\\insleidian2\\core\\config_hot_reload.py',
   'PYMODULE'),
  ('core.window_arrangement_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\window_arrangement_manager.py',
   'PYMODULE'),
  ('core.simple_config',
   'e:\\VScodexiangmu\\insleidian2\\core\\simple_config.py',
   'PYMODULE'),
  ('logging', 'E:\\python-3.12.9\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('core.logger_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\logger_manager.py',
   'PYMODULE'),
  ('core.diagnostics',
   'e:\\VScodexiangmu\\insleidian2\\core\\diagnostics.py',
   'PYMODULE'),
  ('datetime', 'E:\\python-3.12.9\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'E:\\python-3.12.9\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('pathlib', 'E:\\python-3.12.9\\Lib\\pathlib.py', 'PYMODULE'),
  ('argparse', 'E:\\python-3.12.9\\Lib\\argparse.py', 'PYMODULE'),
  ('tracemalloc', 'E:\\python-3.12.9\\Lib\\tracemalloc.py', 'PYMODULE')],
 [('python312.dll', 'E:\\python-3.12.9\\python312.dll', 'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve448.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\PublicKey\\_curve448.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve25519.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\PublicKey\\_curve25519.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('pywin32_system32\\pywintypes312.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\pywin32_system32\\pywintypes312.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom312.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\pywin32_system32\\pythoncom312.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qwindowsvistastyle.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_decimal.pyd', 'E:\\python-3.12.9\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'E:\\python-3.12.9\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'E:\\python-3.12.9\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'E:\\python-3.12.9\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'E:\\python-3.12.9\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'E:\\python-3.12.9\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'E:\\python-3.12.9\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'E:\\python-3.12.9\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'E:\\python-3.12.9\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'E:\\python-3.12.9\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_queue.pyd', 'E:\\python-3.12.9\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'E:\\python-3.12.9\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'E:\\python-3.12.9\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'E:\\python-3.12.9\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'E:\\python-3.12.9\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_sqlite3.pyd', 'E:\\python-3.12.9\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\_yaml.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'E:\\python-3.12.9\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'E:\\python-3.12.9\\VCRUNTIME140.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'E:\\python-3.12.9\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'E:\\python-3.12.9\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'E:\\python-3.12.9\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'E:\\python-3.12.9\\DLLs\\libffi-8.dll', 'BINARY'),
  ('sqlite3.dll', 'E:\\python-3.12.9\\DLLs\\sqlite3.dll', 'BINARY'),
  ('python3.dll', 'E:\\python-3.12.9\\python3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY')],
 [],
 [],
 [('app_config.json',
   'e:\\VScodexiangmu\\insleidian2\\app_config.json',
   'DATA'),
  ('config\\test_instagram_dm.json',
   'e:\\VScodexiangmu\\insleidian2\\config\\test_instagram_dm.json',
   'DATA'),
  ('config\\test_recall_config.json',
   'e:\\VScodexiangmu\\insleidian2\\config\\test_recall_config.json',
   'DATA'),
  ('data\\__init__.py',
   'e:\\VScodexiangmu\\insleidian2\\data\\__init__.py',
   'DATA'),
  ('data\\__pycache__\\__init__.cpython-312.pyc',
   'e:\\VScodexiangmu\\insleidian2\\data\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('data\\__pycache__\\database_manager.cpython-312.pyc',
   'e:\\VScodexiangmu\\insleidian2\\data\\__pycache__\\database_manager.cpython-312.pyc',
   'DATA'),
  ('data\\database_manager.py',
   'e:\\VScodexiangmu\\insleidian2\\data\\database_manager.py',
   'DATA'),
  ('data\\models\\__init__.py',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\__init__.py',
   'DATA'),
  ('data\\models\\__pycache__\\__init__.cpython-312.pyc',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('data\\models\\__pycache__\\emulator_model.cpython-312.pyc',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\__pycache__\\emulator_model.cpython-312.pyc',
   'DATA'),
  ('data\\models\\emulator_model.py',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\emulator_model.py',
   'DATA'),
  ('data\\repositories\\__init__.py',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\__init__.py',
   'DATA'),
  ('data\\repositories\\__pycache__\\__init__.cpython-312.pyc',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('data\\repositories\\__pycache__\\emulator_repository.cpython-312.pyc',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\__pycache__\\emulator_repository.cpython-312.pyc',
   'DATA'),
  ('data\\repositories\\emulator_repository.py',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\emulator_repository.py',
   'DATA'),
  ('img\\V2.png', 'e:\\VScodexiangmu\\insleidian2\\img\\V2.png', 'DATA'),
  ('img\\chehui.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\chehui.png',
   'DATA'),
  ('img\\chehui2.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\chehui2.png',
   'DATA'),
  ('img\\douyin.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\douyin.png',
   'DATA'),
  ('img\\faxiaoxi.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\faxiaoxi.png',
   'DATA'),
  ('img\\ins.png', 'e:\\VScodexiangmu\\insleidian2\\img\\ins.png', 'DATA'),
  ('certifi\\py.typed',
   'E:\\python-3.12.9\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\python-3.12.9\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\INSTALLER',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\WHEEL',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\REQUESTED',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\RECORD',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\METADATA',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cv2\\config.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('cv2\\config-3.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\REQUESTED',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy-2.2.6.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('cv2\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'e:\\VScodexiangmu\\insleidian2\\build\\ins雷电中控\\base_library.zip',
   'DATA')],
 [('genericpath', 'E:\\python-3.12.9\\Lib\\genericpath.py', 'PYMODULE'),
  ('stat', 'E:\\python-3.12.9\\Lib\\stat.py', 'PYMODULE'),
  ('locale', 'E:\\python-3.12.9\\Lib\\locale.py', 'PYMODULE'),
  ('ntpath', 'E:\\python-3.12.9\\Lib\\ntpath.py', 'PYMODULE'),
  ('re._parser', 'E:\\python-3.12.9\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'E:\\python-3.12.9\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'E:\\python-3.12.9\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'E:\\python-3.12.9\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'E:\\python-3.12.9\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('weakref', 'E:\\python-3.12.9\\Lib\\weakref.py', 'PYMODULE'),
  ('posixpath', 'E:\\python-3.12.9\\Lib\\posixpath.py', 'PYMODULE'),
  ('codecs', 'E:\\python-3.12.9\\Lib\\codecs.py', 'PYMODULE'),
  ('reprlib', 'E:\\python-3.12.9\\Lib\\reprlib.py', 'PYMODULE'),
  ('sre_parse', 'E:\\python-3.12.9\\Lib\\sre_parse.py', 'PYMODULE'),
  ('functools', 'E:\\python-3.12.9\\Lib\\functools.py', 'PYMODULE'),
  ('_collections_abc',
   'E:\\python-3.12.9\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('collections.abc',
   'E:\\python-3.12.9\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'E:\\python-3.12.9\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('linecache', 'E:\\python-3.12.9\\Lib\\linecache.py', 'PYMODULE'),
  ('abc', 'E:\\python-3.12.9\\Lib\\abc.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'E:\\python-3.12.9\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'E:\\python-3.12.9\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'E:\\python-3.12.9\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'E:\\python-3.12.9\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'E:\\python-3.12.9\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'E:\\python-3.12.9\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'E:\\python-3.12.9\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'E:\\python-3.12.9\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'E:\\python-3.12.9\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'E:\\python-3.12.9\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'E:\\python-3.12.9\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem', 'E:\\python-3.12.9\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'E:\\python-3.12.9\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'E:\\python-3.12.9\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'E:\\python-3.12.9\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'E:\\python-3.12.9\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'E:\\python-3.12.9\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'E:\\python-3.12.9\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'E:\\python-3.12.9\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'E:\\python-3.12.9\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'E:\\python-3.12.9\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'E:\\python-3.12.9\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'E:\\python-3.12.9\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312',
   'E:\\python-3.12.9\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'E:\\python-3.12.9\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'E:\\python-3.12.9\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'E:\\python-3.12.9\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'E:\\python-3.12.9\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'E:\\python-3.12.9\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'E:\\python-3.12.9\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'E:\\python-3.12.9\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'E:\\python-3.12.9\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'E:\\python-3.12.9\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'E:\\python-3.12.9\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'E:\\python-3.12.9\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'E:\\python-3.12.9\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'E:\\python-3.12.9\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'E:\\python-3.12.9\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'E:\\python-3.12.9\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'E:\\python-3.12.9\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'E:\\python-3.12.9\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'E:\\python-3.12.9\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'E:\\python-3.12.9\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'E:\\python-3.12.9\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'E:\\python-3.12.9\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'E:\\python-3.12.9\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'E:\\python-3.12.9\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'E:\\python-3.12.9\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'E:\\python-3.12.9\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'E:\\python-3.12.9\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'E:\\python-3.12.9\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'E:\\python-3.12.9\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'E:\\python-3.12.9\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'E:\\python-3.12.9\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'E:\\python-3.12.9\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'E:\\python-3.12.9\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'E:\\python-3.12.9\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'E:\\python-3.12.9\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'E:\\python-3.12.9\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'E:\\python-3.12.9\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'E:\\python-3.12.9\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings', 'E:\\python-3.12.9\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('operator', 'E:\\python-3.12.9\\Lib\\operator.py', 'PYMODULE'),
  ('types', 'E:\\python-3.12.9\\Lib\\types.py', 'PYMODULE'),
  ('io', 'E:\\python-3.12.9\\Lib\\io.py', 'PYMODULE'),
  ('sre_constants', 'E:\\python-3.12.9\\Lib\\sre_constants.py', 'PYMODULE'),
  ('enum', 'E:\\python-3.12.9\\Lib\\enum.py', 'PYMODULE'),
  ('sre_compile', 'E:\\python-3.12.9\\Lib\\sre_compile.py', 'PYMODULE'),
  ('_weakrefset', 'E:\\python-3.12.9\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('copyreg', 'E:\\python-3.12.9\\Lib\\copyreg.py', 'PYMODULE'),
  ('heapq', 'E:\\python-3.12.9\\Lib\\heapq.py', 'PYMODULE'),
  ('keyword', 'E:\\python-3.12.9\\Lib\\keyword.py', 'PYMODULE'),
  ('cv2',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\__init__.py',
   'PYMODULE'),
  ('cv2.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\version.py',
   'PYMODULE'),
  ('cv2.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\utils\\__init__.py',
   'PYMODULE'),
  ('cv2.typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\typing\\__init__.py',
   'PYMODULE'),
  ('cv2.dnn', '-', 'PYMODULE'),
  ('cv2.gapi.wip.draw', '-', 'PYMODULE'),
  ('cv2.gapi.wip', '-', 'PYMODULE'),
  ('cv2.misc.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\misc\\version.py',
   'PYMODULE'),
  ('cv2.misc',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\misc\\__init__.py',
   'PYMODULE'),
  ('cv2.mat_wrapper',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'PYMODULE'),
  ('cv2.gapi',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\gapi\\__init__.py',
   'PYMODULE'),
  ('cv2.data',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\data\\__init__.py',
   'PYMODULE'),
  ('cv2.config-3',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\config-3.py',
   'PYMODULE'),
  ('cv2.config',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\config.py',
   'PYMODULE'),
  ('cv2.load_config_py3',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\load_config_py3.py',
   'PYMODULE'),
  ('os', 'E:\\python-3.12.9\\Lib\\os.py', 'PYMODULE'),
  ('traceback', 'E:\\python-3.12.9\\Lib\\traceback.py', 'PYMODULE'),
  ('warnings', 'E:\\python-3.12.9\\Lib\\warnings.py', 'PYMODULE')])
