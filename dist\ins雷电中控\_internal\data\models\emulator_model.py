#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 模拟器数据模型 - 替换EmulatorInfo
========================================
功能描述: 统一的模拟器数据结构定义和验证
主要方法: validate(), to_dict(), from_dict(), from_db_row()
调用关系: 被EmulatorRepository和业务层使用
注意事项:
- 替换原有的EmulatorInfo dataclass
- 添加数据验证逻辑
- 支持数据库行和字典的转换
- 类型安全和默认值处理
========================================
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any
from datetime import datetime
import sqlite3
from pathlib import Path

from core.logger_manager import log_error


@dataclass
class EmulatorModel:
    """模拟器数据模型 - 替换原有的EmulatorInfo"""
    
    id: int
    name: str
    path: str = ""
    # 🎯 删除运行时状态字段 - 状态通过实时检测获取，不存储在模型中
    # 删除：status, pid, control_port, adb_port, last_check
    # 🎯 保留用户配置字段
    cpu: int = 2
    memory: int = 2048
    width: int = 540
    height: int = 960
    dpi: int = 240
    assigned_tasks: str = ""
    priority: int = 0
    notes: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        # 🎯 删除last_check字段处理，确保时间字段的正确类型
        if isinstance(self.created_at, str):
            self.created_at = self._parse_datetime(self.created_at)
        if isinstance(self.updated_at, str):
            self.updated_at = self._parse_datetime(self.updated_at)
    
    def _parse_datetime(self, dt_str: str) -> Optional[datetime]:
        """解析日期时间字符串"""
        if not dt_str:
            return None
        try:
            return datetime.fromisoformat(dt_str)
        except ValueError:
            log_error(f"日期时间解析失败: {dt_str}", component="EmulatorModel")
            return None
    
    def validate(self) -> bool:
        """
        验证模拟器数据的完整性
        
        Returns:
            数据是否有效
        """
        try:
            # 🎯 基础字段验证
            if not isinstance(self.id, int) or self.id <= 0:
                log_error(f"无效的模拟器ID: {self.id}", component="EmulatorModel")
                return False
            
            if not self.name or not isinstance(self.name, str):
                log_error(f"无效的模拟器名称: {self.name}", component="EmulatorModel")
                return False
            
            if not self.path or not isinstance(self.path, str):
                log_error(f"无效的模拟器路径: {self.path}", component="EmulatorModel")
                return False
            
            # 🎯 删除运行时状态验证 - 状态通过实时检测获取
            # 🎯 保留用户配置验证
            if self.cpu < 1 or self.cpu > 8:
                log_error(f"无效的CPU核心数: {self.cpu}", component="EmulatorModel")
                return False

            if self.memory < 512 or self.memory > 8192:
                log_error(f"无效的内存大小: {self.memory}MB", component="EmulatorModel")
                return False

            if self.priority < 0 or self.priority > 10:
                log_error(f"无效的优先级: {self.priority}", component="EmulatorModel")
                return False
            
            return True
            
        except Exception as e:
            log_error(f"数据验证异常: {e}", component="EmulatorModel")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            字典表示的模拟器数据
        """
        return {
            'id': self.id,
            'name': self.name,
            'path': self.path,
            # 🎯 删除运行时状态字段，保留用户配置字段
            'cpu': self.cpu,
            'memory': self.memory,
            'width': self.width,
            'height': self.height,
            'dpi': self.dpi,
            'assigned_tasks': self.assigned_tasks,
            'priority': self.priority,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EmulatorModel':
        """
        从字典创建模拟器模型
        
        Args:
            data: 字典数据
            
        Returns:
            模拟器模型实例
        """
        return cls(
            id=data.get('id', 0),
            name=data.get('name', ''),
            path=data.get('path', ''),
            # 🎯 删除运行时状态字段，保留用户配置字段
            cpu=data.get('cpu', 2),
            memory=data.get('memory', 2048),
            width=data.get('width', 540),
            height=data.get('height', 960),
            dpi=data.get('dpi', 240),
            assigned_tasks=data.get('assigned_tasks', ''),
            priority=data.get('priority', 0),
            notes=data.get('notes', ''),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )
    
    @classmethod
    def from_db_row(cls, row: Dict[str, Any]) -> 'EmulatorModel':
        """
        从数据库行创建模拟器模型
        
        Args:
            row: 数据库行数据
            
        Returns:
            模拟器模型实例
        """
        return cls(
            id=row['id'],
            name=row['name'],
            path=row['path'],
            # 🎯 删除运行时状态字段，保留用户配置字段
            cpu=row.get('cpu', 2),
            memory=row.get('memory', 2048),
            width=row.get('width', 540),
            height=row.get('height', 960),
            dpi=row.get('dpi', 240),
            assigned_tasks=row.get('assigned_tasks', ''),
            priority=row.get('priority', 0),
            notes=row.get('notes', ''),
            created_at=row.get('created_at'),
            updated_at=row.get('updated_at')
        )
    
    # 🎯 删除运行时状态相关方法 - 状态通过实时检测获取
    # 删除：is_running(), get_display_status(), update_status()

    def update_config(self, **kwargs):
        """
        更新模拟器配置

        Args:
            **kwargs: 配置参数
        """
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.now()
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"EmulatorModel(id={self.id}, name='{self.name}', path='{self.path}')"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"EmulatorModel(id={self.id}, name='{self.name}', path='{self.path}', "
                f"cpu={self.cpu}, memory={self.memory}, priority={self.priority})")


# 🎯 兼容性别名 - 逐步替换EmulatorInfo
EmulatorInfo = EmulatorModel
