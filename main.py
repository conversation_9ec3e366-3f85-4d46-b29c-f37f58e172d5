#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 雷电模拟器中控系统 - 重构后的主程序入口
========================================
功能描述: 简化的应用程序入口，使用统一的重构后系统
主要方法: main(), create_components(), connect_components()
调用关系: 程序入口点，创建并协调重构后的核心组件
注意事项:
- 使用简化配置管理器（SimpleConfigManager）
- 使用统一模拟器管理器（UnifiedEmulatorManager）
- 使用简化异步桥梁（SimpleAsyncBridge）
- 使用简化日志系统（基于Python标准logging）1
========================================
"""

import sys
import warnings
import argparse
import traceback
import os
from pathlib import Path
from datetime import datetime

# 🚨 全局异常捕获和错误日志系统
def setup_global_exception_handler():
    """设置全局异常处理器"""
    # 创建错误日志目录
    error_log_dir = Path("error_logs")
    error_log_dir.mkdir(exist_ok=True)

    # 创建错误日志文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    error_log_file = error_log_dir / f"crash_report_{timestamp}.log"

    def handle_exception(exc_type, exc_value, exc_traceback):
        """全局异常处理函数"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许Ctrl+C正常退出
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        # 创建详细的错误报告
        error_info = {
            "timestamp": datetime.now().isoformat(),
            "exception_type": exc_type.__name__,
            "exception_message": str(exc_value),
            "traceback": traceback.format_exception(exc_type, exc_value, exc_traceback),
            "python_version": sys.version,
            "working_directory": os.getcwd(),
            "command_line_args": sys.argv,
            "environment_info": {
                "PATH": os.environ.get("PATH", ""),
                "PYTHONPATH": os.environ.get("PYTHONPATH", ""),
                "TEMP": os.environ.get("TEMP", ""),
            }
        }

        # 写入错误日志文件
        try:
            with open(error_log_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("🚨 程序崩溃报告\n")
                f.write("=" * 80 + "\n")
                f.write(f"时间: {error_info['timestamp']}\n")
                f.write(f"异常类型: {error_info['exception_type']}\n")
                f.write(f"异常消息: {error_info['exception_message']}\n")
                f.write(f"Python版本: {error_info['python_version']}\n")
                f.write(f"工作目录: {error_info['working_directory']}\n")
                f.write(f"命令行参数: {error_info['command_line_args']}\n")
                f.write("\n" + "=" * 80 + "\n")
                f.write("📍 详细堆栈跟踪:\n")
                f.write("=" * 80 + "\n")
                f.write(''.join(error_info['traceback']))
                f.write("\n" + "=" * 80 + "\n")
                f.write("🔧 环境信息:\n")
                f.write("=" * 80 + "\n")
                for key, value in error_info['environment_info'].items():
                    f.write(f"{key}: {value}\n")
        except Exception as log_error:
            print(f"无法写入错误日志: {log_error}")

        # 显示用户友好的错误消息
        try:
            from PyQt6.QtWidgets import QApplication, QMessageBox
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)

            error_msg = f"""程序遇到错误并需要关闭：

错误类型: {error_info['exception_type']}
错误信息: {error_info['exception_message']}

详细错误日志已保存到:
{error_log_file}

请将此日志文件发送给开发者以获得帮助。"""

            QMessageBox.critical(None, "程序错误", error_msg)
        except Exception:
            # 如果GUI显示失败，使用控制台输出
            print("=" * 60)
            print("🚨 程序遇到严重错误")
            print("=" * 60)
            print(f"错误类型: {error_info['exception_type']}")
            print(f"错误信息: {error_info['exception_message']}")
            print(f"错误日志: {error_log_file}")
            print("=" * 60)
            print("详细堆栈跟踪:")
            print(''.join(error_info['traceback']))

    # 设置全局异常处理器
    sys.excepthook = handle_exception

    return error_log_file

# 🚨 立即设置全局异常处理
GLOBAL_ERROR_LOG = setup_global_exception_handler()

# 🎯 过滤PyQt6兼容性警告
warnings.filterwarnings("ignore", category=DeprecationWarning, message=".*sipPyTypeDict.*")

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="雷电模拟器中控系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py              # 正常启动
  python main.py --debug      # 启动诊断模式
  python main.py --debug --no-ui  # 仅运行诊断，不启动UI
        """
    )

    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用详细的启动诊断模式'
    )

    parser.add_argument(
        '--no-ui',
        action='store_true',
        help='仅运行诊断，不启动UI界面'
    )

    return parser.parse_args()


def create_startup_log():
    """创建启动日志文件"""
    try:
        log_dir = Path("startup_logs")
        log_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"startup_{timestamp}.log"

        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("🚀 程序启动日志\n")
            f.write("=" * 80 + "\n")
            f.write(f"启动时间: {datetime.now()}\n")
            f.write(f"Python版本: {sys.version}\n")
            f.write(f"工作目录: {os.getcwd()}\n")
            f.write(f"命令行参数: {sys.argv}\n")
            f.write(f"错误日志文件: {GLOBAL_ERROR_LOG}\n")
            f.write("=" * 80 + "\n\n")

        return log_file
    except Exception as e:
        print(f"⚠️ 无法创建启动日志: {e}")
        return None

def log_startup_step(log_file, step, message, success=True):
    """记录启动步骤"""
    if not log_file:
        return

    try:
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        status = "✅" if success else "❌"
        log_entry = f"[{timestamp}] {status} {step}: {message}\n"

        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)

        # 同时输出到控制台（如果是调试模式）
        print(f"[STARTUP] {log_entry.strip()}")

    except Exception as e:
        print(f"⚠️ 无法写入启动日志: {e}")

def run_startup_diagnostics_if_needed(debug_mode: bool, no_ui: bool = False):
    """根据参数运行启动诊断"""
    # 🚨 强制启用诊断模式（特别是打包版本）
    force_diagnostics = debug_mode or no_ui or getattr(sys, 'frozen', False)

    if force_diagnostics:
        print("🔍 正在运行启动诊断...")

        try:
            from core.diagnostics import run_startup_diagnostics

            # 运行诊断
            diagnostics = run_startup_diagnostics(debug_mode=True)

            # 显示报告
            diagnostics.print_summary()

            # 如果只是诊断模式，检查后退出
            if no_ui:
                report = diagnostics.generate_report()
                if report['errors']:
                    print("\n❌ 发现关键错误，程序无法正常启动")
                    sys.exit(1)
                else:
                    print("\n✅ 诊断完成，程序可以正常启动")
                    sys.exit(0)

            # 如果有关键错误，询问是否继续（仅在非打包版本）
            report = diagnostics.generate_report()
            if report['errors'] and not getattr(sys, 'frozen', False):
                print("\n⚠️ 发现关键错误，可能影响程序正常运行")
                response = input("是否继续启动? (y/N): ").strip().lower()
                if response not in ['y', 'yes']:
                    print("用户选择退出")
                    sys.exit(1)

            return diagnostics

        except Exception as e:
            print(f"❌ 诊断系统运行失败: {e}")
            traceback.print_exc()
            if no_ui:
                sys.exit(1)
            return None

    return None


def setup_logging():
    """设置简化日志系统"""
    from core.logger_manager import setup_logging, log_runtime

    # 初始化简化日志系统
    setup_logging()
    log_runtime("应用程序启动", component="main")

    import logging
    return logging.getLogger(__name__)

def create_components(logger):
    """🎯 创建核心组件 - 删除重复的模拟器管理器返回"""
    from core.simple_config import get_config_manager
    from core.async_bridge import get_async_bridge

    # 🎯 创建统一配置管理器
    config_manager = get_config_manager()
    logger.info("统一配置管理器已创建")

    # 🎯 创建修复版异步桥梁（内部会创建模拟器管理器）
    bridge = get_async_bridge()
    logger.info("修复版异步桥梁已创建")

    return config_manager, bridge

def create_ui_components(config_manager, logger):
    """创建UI组件"""
    from core.logger_manager import log_runtime
    from ui.main_window_v2 import MainWindowV2
    main_window = MainWindowV2(config_manager=config_manager)
    logger.info("UI主窗口已创建")
    log_runtime("UI主窗口已创建", component="MainWindowV2")
    return main_window

def connect_components(main_window, bridge, logger):
    """🎯 连接UI和业务层组件 - 专注应用程序协调"""

    # 🎯 设置async_bridge引用，让UI可以访问
    main_window.async_bridge = bridge

    # 🎯 只负责UI和桥梁的连接，不涉及具体业务逻辑
    # 注意：batch_operation_requested信号连接在bridge.connect_task_signals_to_ui中处理，避免重复连接
    bridge.operation_completed.connect(main_window.on_operation_completed)
    bridge.operation_failed.connect(main_window.on_operation_failed)
    bridge.operation_progress.connect(main_window.on_operation_progress)

    # 🎯 通过桥梁连接所有组件，避免main.py直接操作业务层
    bridge.connect_task_signals_to_ui(main_window)
    bridge.connect_monitor_to_ui(main_window)

    # 🎯 关键修复：异步桥梁连接完成后，处理延迟的扫描请求
    if hasattr(main_window, 'process_pending_scan_requests'):
        main_window.process_pending_scan_requests()

    logger.info("UI层和业务层已连接")

def start_services(bridge, logger):
    """🎯 启动后台服务 - 通过桥梁启动，专注应用程序协调"""
    # 🎯 通过异步桥梁启动服务，不直接操作业务层
    bridge.start_background_services()
    logger.info("后台服务已启动")

def cleanup_resources(config_manager, bridge, logger):
    """清理应用程序资源 - 恢复异步桥梁版"""
    logger.info("开始清理资源...")

    # 保存配置
    try:
        config_manager.save()
        logger.info("配置已保存")
    except Exception as e:
        logger.error(f"配置保存失败: {e}")

    # 清理异步桥梁
    try:
        bridge.shutdown()
        logger.info("异步桥梁已关闭")
    except Exception as e:
        logger.error(f"异步桥梁关闭失败: {e}")

    logger.info("应用程序已退出")

# ========================================
# 🎯 主函数 - 应用程序生命周期管理
# ========================================
# 功能描述: 管理应用程序完整生命周期，从启动到关闭的所有流程
# 主要方法: main(), cleanup_resources()
# 调用关系: 程序入口点，协调所有组件的创建、连接、启动和清理
# 注意事项: 使用try-finally确保资源正确清理，支持异常处理和优雅退出
# ========================================

def show_auth_dialog():
    """🎯 显示认证对话框 - 按照官方指导"""
    from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                               QLineEdit, QComboBox, QCheckBox, QPushButton,
                               QTextEdit, QMessageBox, QApplication)
    from PyQt6.QtCore import Qt
    import json
    import os

    class SimpleAuthDialog(QDialog):
        def __init__(self):
            super().__init__()
            self.auth = None  # 🎯 延迟创建Auth实例
            self.auth_success = False
            self.setup_ui()
            self.load_saved_card()

            # 🎯 创建定时器用于线程安全的UI更新
            from PyQt6.QtCore import QTimer
            self.ui_update_timer = QTimer()
            self.ui_update_timer.timeout.connect(self._check_auth_result)
            self.auth_result = None

        def setup_ui(self):
            self.setWindowTitle("雷电模拟器中控系统 - 授权验证")
            self.setFixedSize(400, 350)
            self.setModal(True)

            layout = QVBoxLayout(self)

            # 标题
            title = QLabel("🎯 雷电模拟器中控系统")
            title.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
            layout.addWidget(title)

            # 卡号输入
            layout.addWidget(QLabel("授权卡号:"))
            self.card_input = QLineEdit()
            self.card_input.setPlaceholderText("请输入您的授权卡号")
            layout.addWidget(self.card_input)

            # 线路选择
            layout.addWidget(QLabel("主题风格:"))
            self.route_combo = QComboBox()
            self.route_combo.addItems([
                "风格1",
                "风格2",
                "风格3",
                "风格4"
            ])
            layout.addWidget(self.route_combo)

            # 记住卡密
            self.remember_checkbox = QCheckBox("记住卡密")
            layout.addWidget(self.remember_checkbox)

            # 公告显示
            layout.addWidget(QLabel("系统公告:"))
            self.notice_text = QTextEdit()
            self.notice_text.setFixedHeight(80)
            self.notice_text.setReadOnly(True)
            layout.addWidget(self.notice_text)

            # 按钮
            btn_layout = QHBoxLayout()
            self.verify_btn = QPushButton("开始验证")
            self.verify_btn.clicked.connect(self.verify_auth)
            btn_layout.addWidget(self.verify_btn)

            cancel_btn = QPushButton("取消")
            cancel_btn.clicked.connect(self.reject)
            btn_layout.addWidget(cancel_btn)

            layout.addLayout(btn_layout)

            # 🎯 设置默认公告，不在初始化时加载（避免延迟）
            self.notice_text.setPlainText("点击【开始验证】获取最新公告信息")

        def load_app_info(self):
            """🎯 延迟加载应用信息，只在验证时创建Auth实例"""
            self.notice_text.setPlainText("正在获取系统公告...")

            try:
                # 🎯 在需要时才创建Auth实例
                if self.auth is None:
                    from core.authsdk import Auth
                    self.auth = Auth()

                result = self.auth.appinfo(0)
                code = self.auth.rsa_decrypt(result['code'])
                if code == '200':
                    notice = self.auth.rsa_decrypt(result['notice'])
                    self.notice_text.setPlainText(notice)
            except Exception as e:
                print(f"获取应用信息失败: {e}")
                self.notice_text.setPlainText("获取公告失败")

        def load_saved_card(self):
            try:
                if os.path.exists('auth_config.json'):
                    with open('auth_config.json', 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        if config.get('remember_card'):
                            self.card_input.setText(config.get('card_number', ''))
                            self.remember_checkbox.setChecked(True)
                        self.route_combo.setCurrentIndex(config.get('route_index', 0))
            except:
                pass

        def save_card_config(self):
            try:
                config = {
                    'remember_card': self.remember_checkbox.isChecked(),
                    'card_number': self.card_input.text() if self.remember_checkbox.isChecked() else '',
                    'route_index': self.route_combo.currentIndex()
                }
                with open('auth_config.json', 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
            except:
                pass

        def verify_auth(self):
            card_number = self.card_input.text().strip()
            if not card_number:
                QMessageBox.warning(self, "错误", "请输入授权卡号")
                return

            # 🎯 使用线程避免UI卡顿
            self.verify_btn.setEnabled(False)
            self.verify_btn.setText("验证中...")

            # 获取线路设置
            route_index = self.route_combo.currentIndex()

            # 在后台线程中执行认证
            import threading
            auth_thread = threading.Thread(
                target=self._verify_in_background,
                args=(card_number, route_index),
                daemon=True
            )
            auth_thread.start()

            # 🎯 启动定时器检查结果
            self.ui_update_timer.start(100)  # 每100ms检查一次

        def _verify_in_background(self, card_number, route_index):
            """🎯 在后台线程中执行认证，避免UI卡顿 - 增强版错误处理"""
            try:
                print(f"🔍 开始后台认证: card_number={card_number}, route_index={route_index}")

                # 🚨 详细的Auth实例创建
                if self.auth is None:
                    print("🔍 创建Auth实例...")
                    try:
                        from core.authsdk import Auth
                        self.auth = Auth()
                        print(f"✅ Auth实例创建成功: {type(self.auth)}")
                    except Exception as e:
                        print(f"🚨 CRITICAL: Auth实例创建失败: {e}")
                        raise

                # 🚨 详细的路由设置
                routes = [
                    "http://api.kushao.net/v3/",        # 风格1
                    "http://api.kushao.018888.xyz/v3/", # 风格2
                    "http://api.ks.186777.xyz/v3/",     # 风格3
                    "http://api.kushao.net/v3/"         # 风格4 (与风格1相同)
                ]

                if route_index < 0 or route_index >= len(routes):
                    print(f"🚨 CRITICAL: 无效的路由索引: {route_index}")
                    raise ValueError(f"无效的路由索引: {route_index}")

                selected_route = routes[route_index]
                print(f"🔍 设置认证路由: {selected_route}")
                self.auth.api = selected_route

                # 🚨 详细的认证执行
                print("🔍 开始执行认证...")
                try:
                    # 🎯 并行执行公告获取和认证验证
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                        # 同时执行两个操作
                        future_info = executor.submit(self._load_app_info_background)
                        future_verify = executor.submit(self.auth.verify, card_number)

                        # 等待验证结果（公告可以后台继续加载）
                        print("🔍 等待认证结果...")
                        result = future_verify.result()
                        print(f"🔍 认证结果类型: {type(result)}")
                        print(f"🔍 认证结果内容: {result}")

                        # 尝试获取公告结果（如果还没完成就跳过）
                        try:
                            future_info.result(timeout=0.1)
                        except:
                            pass  # 公告获取可以继续在后台进行

                except Exception as e:
                    print(f"🚨 CRITICAL: 认证执行失败: {type(e).__name__}: {e}")
                    import traceback
                    print(f"🚨 认证异常堆栈: {traceback.format_exc()}")
                    raise

                # 🚨 详细验证result结构
                if result is None:
                    print("🚨 CRITICAL: 认证结果为None")
                    raise ValueError("认证结果为None")

                print(f"✅ 认证执行完成，准备存储结果")

                # 🎯 将结果存储，通过定时器更新UI
                self.auth_result = {
                    'type': 'result',
                    'data': result,
                    'card_number': card_number
                }
                print(f"✅ 认证结果已存储: {self.auth_result}")

            except Exception as e:
                # 🚨 详细的异常处理
                error_msg = f"{type(e).__name__}: {e}"
                print(f"🚨 CRITICAL: 后台认证异常: {error_msg}")

                import traceback
                stack_trace = traceback.format_exc()
                print(f"🚨 完整异常堆栈: {stack_trace}")

                # 异常处理
                self.auth_result = {
                    'type': 'error',
                    'message': error_msg,
                    'stack_trace': stack_trace
                }
                print(f"🚨 异常结果已存储: {self.auth_result}")

        def _load_app_info_background(self):
            """🎯 在后台线程中加载应用信息"""
            try:
                result = self.auth.appinfo(0)
                if result and 'code' in result:
                    code = self.auth.rsa_decrypt(result['code'])
                    if code == '200':
                        notice = self.auth.rsa_decrypt(result['notice'])
                        # 存储公告信息，通过定时器更新UI
                        self.notice_content = notice
            except Exception as e:
                print(f"获取应用信息失败: {e}")
                self.notice_content = "获取公告失败"

        def _check_auth_result(self):
            """🎯 定时器检查认证结果并更新UI - 增强版错误处理"""
            try:
                # 🚨 安全更新公告内容
                if hasattr(self, 'notice_content'):
                    try:
                        self.notice_text.setPlainText(self.notice_content)
                        delattr(self, 'notice_content')
                    except Exception as e:
                        print(f"🚨 更新公告内容失败: {e}")

                # 🚨 详细检查认证结果
                if self.auth_result is not None:
                    result = self.auth_result
                    self.auth_result = None
                    self.ui_update_timer.stop()

                    print(f"🔍 认证结果类型: {type(result)}")
                    print(f"🔍 认证结果内容: {result}")

                    # 🚨 详细验证result结构
                    if not isinstance(result, dict):
                        print(f"🚨 CRITICAL: result不是字典类型: {type(result)}")
                        QMessageBox.critical(self, "错误", f"认证结果格式错误: {type(result)}")
                        self._reset_verify_button()
                        return

                    if 'type' not in result:
                        print(f"🚨 CRITICAL: result缺少type字段: {result}")
                        QMessageBox.critical(self, "错误", "认证结果缺少类型信息")
                        self._reset_verify_button()
                        return

                    if result['type'] == 'result':
                        # 🚨 详细验证data字段
                        if 'data' not in result:
                            print(f"🚨 CRITICAL: result缺少data字段: {result}")
                            QMessageBox.critical(self, "错误", "认证结果缺少数据信息")
                            self._reset_verify_button()
                            return

                        auth_result = result['data']
                        print(f"🔍 auth_result类型: {type(auth_result)}")
                        print(f"🔍 auth_result内容: {auth_result}")

                        # 🚨 详细验证auth_result结构
                        if auth_result is None:
                            print(f"🚨 CRITICAL: auth_result为None")
                            QMessageBox.critical(self, "错误", "认证数据为空")
                            self._reset_verify_button()
                            return

                        if not isinstance(auth_result, dict):
                            print(f"🚨 CRITICAL: auth_result不是字典类型: {type(auth_result)}")
                            QMessageBox.critical(self, "错误", f"认证数据格式错误: {type(auth_result)}")
                            self._reset_verify_button()
                            return

                        if 'code' not in auth_result:
                            print(f"🚨 CRITICAL: auth_result缺少code字段: {auth_result}")
                            QMessageBox.critical(self, "错误", "认证数据缺少状态码")
                            self._reset_verify_button()
                            return

                        # 🚨 安全访问code字段
                        try:
                            code = auth_result['code']
                            print(f"🔍 认证状态码: {code} (类型: {type(code)})")
                        except Exception as e:
                            print(f"🚨 CRITICAL: 访问code字段失败: {e}")
                            QMessageBox.critical(self, "错误", f"无法读取认证状态: {e}")
                            self._reset_verify_button()
                            return

                        if code == 200:
                            # 认证成功
                            try:
                                self.save_card_config()
                                msg = auth_result.get('msg', '认证成功')
                                QMessageBox.information(self, "成功", msg)
                                self.auth_success = True

                                # 启动心跳检测
                                import threading
                                heartbeat_thread = threading.Thread(target=self.auth.heart_beat, daemon=True)
                                heartbeat_thread.start()

                                self.accept()
                            except Exception as e:
                                print(f"🚨 处理认证成功时出错: {e}")
                                QMessageBox.critical(self, "错误", f"处理认证成功时出错: {e}")
                                self._reset_verify_button()

                        elif code == 104:
                            # 换机处理
                            try:
                                card_number = result.get('card_number', '')
                                self.auth.change_device(card_number)
                                QMessageBox.warning(self, "换机", "检测到换机，请重新验证")
                                self._reset_verify_button()
                            except Exception as e:
                                print(f"🚨 处理换机时出错: {e}")
                                QMessageBox.critical(self, "错误", f"处理换机时出错: {e}")
                                self._reset_verify_button()
                        else:
                            # 认证失败
                            try:
                                msg = auth_result.get('msg', '验证失败')
                                QMessageBox.critical(self, "失败", f"验证失败: {msg}")
                                self._reset_verify_button()
                            except Exception as e:
                                print(f"🚨 处理认证失败时出错: {e}")
                                QMessageBox.critical(self, "错误", f"处理认证失败时出错: {e}")
                                self._reset_verify_button()

                    elif result['type'] == 'error':
                        # 异常处理
                        try:
                            message = result.get('message', '未知错误')
                            QMessageBox.critical(self, "错误", f"验证过程出错: {message}")
                            self._reset_verify_button()
                        except Exception as e:
                            print(f"🚨 处理错误信息时出错: {e}")
                            QMessageBox.critical(self, "错误", f"处理错误信息时出错: {e}")
                            self._reset_verify_button()
                    else:
                        print(f"🚨 未知的result类型: {result['type']}")
                        QMessageBox.critical(self, "错误", f"未知的认证结果类型: {result['type']}")
                        self._reset_verify_button()

            except Exception as e:
                print(f"🚨 CRITICAL: _check_auth_result异常: {type(e).__name__}: {e}")
                import traceback
                print(f"🚨 异常堆栈: {traceback.format_exc()}")
                try:
                    QMessageBox.critical(self, "严重错误", f"认证检查过程出现严重错误: {e}")
                    self._reset_verify_button()
                except:
                    print("🚨 连显示错误对话框都失败了")

        def _reset_verify_button(self):
            """🎯 重置验证按钮状态"""
            try:
                self.verify_btn.setEnabled(True)
                self.verify_btn.setText("开始验证")
            except Exception as e:
                print(f"🚨 重置按钮状态失败: {e}")

    # 显示认证对话框
    dialog = SimpleAuthDialog()
    if dialog.exec() == QDialog.DialogCode.Accepted and dialog.auth_success:
        return True
    else:
        return False

def main():
    """🎯 主函数 - 修复版启动序列，确保正确的初始化顺序"""
    startup_log = None

    try:
        # 🚀 创建启动日志
        startup_log = create_startup_log()
        log_startup_step(startup_log, "INIT", "程序启动开始")

        # 解析命令行参数
        log_startup_step(startup_log, "ARGS", "开始解析命令行参数")
        args = parse_arguments()
        log_startup_step(startup_log, "ARGS", f"命令行参数解析完成: debug={getattr(args, 'debug', False)}, no_ui={getattr(args, 'no_ui', False)}")

        # 运行启动诊断（如果需要）
        log_startup_step(startup_log, "DIAG", "开始启动诊断")
        diagnostics = run_startup_diagnostics_if_needed(args.debug, args.no_ui)
        log_startup_step(startup_log, "DIAG", "启动诊断完成")

        # 🎯 首先进行酷烧云认证验证
        log_startup_step(startup_log, "AUTH", "开始认证验证")
        from PyQt6.QtWidgets import QApplication
        temp_app = QApplication(sys.argv)
        log_startup_step(startup_log, "AUTH", "临时QApplication创建成功")

        if not show_auth_dialog():
            log_startup_step(startup_log, "AUTH", "认证失败，程序退出", False)
            sys.exit(1)

        log_startup_step(startup_log, "AUTH", "认证成功")
        temp_app.quit()
        log_startup_step(startup_log, "AUTH", "临时QApplication已退出")

        # 导入PyQt6
        log_startup_step(startup_log, "IMPORT", "开始导入PyQt6组件")
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTimer
        log_startup_step(startup_log, "IMPORT", "PyQt6组件导入成功")

        # 设置日志
        log_startup_step(startup_log, "LOG", "开始设置日志系统")
        logger = setup_logging()
        log_startup_step(startup_log, "LOG", "日志系统设置完成")

        if args.debug:
            logger.info("🔍 调试模式已启用")
            log_startup_step(startup_log, "DEBUG", "调试模式已启用")

        # 创建应用程序
        log_startup_step(startup_log, "APP", "开始创建Qt应用程序")
        app = QApplication(sys.argv)
        logger.info("Qt应用程序已创建")
        log_startup_step(startup_log, "APP", "Qt应用程序创建成功")

        # 创建核心组件
        log_startup_step(startup_log, "CORE", "开始创建核心组件")
        config_manager, bridge = create_components(logger)
        log_startup_step(startup_log, "CORE", "核心组件创建成功")

        # 创建UI组件
        log_startup_step(startup_log, "UI", "开始创建UI组件")
        main_window = create_ui_components(config_manager, logger)
        log_startup_step(startup_log, "UI", "UI组件创建成功")

        # 🎯 显示主窗口 - 在连接组件前显示，确保UI完全准备好
        log_startup_step(startup_log, "SHOW", "开始显示主窗口")
        main_window.show()
        logger.info("主窗口已显示")
        log_startup_step(startup_log, "SHOW", "主窗口显示成功")

        # 处理待处理的事件，确保UI完全渲染
        log_startup_step(startup_log, "EVENT", "处理待处理事件")
        app.processEvents()
        log_startup_step(startup_log, "EVENT", "事件处理完成")

        # 连接组件 - UI显示后再连接
        log_startup_step(startup_log, "CONNECT", "开始连接组件")
        connect_components(main_window, bridge, logger)
        log_startup_step(startup_log, "CONNECT", "组件连接成功")

        # 🎯 延迟启动服务 - 确保UI和信号连接完全准备好
        def delayed_start_services():
            log_startup_step(startup_log, "SERVICE", "开始启动延迟服务")
            start_services(bridge, logger)
            logger.info("延迟启动服务完成")
            log_startup_step(startup_log, "SERVICE", "延迟服务启动成功")

        # 使用QTimer延迟1秒启动监控服务
        log_startup_step(startup_log, "TIMER", "设置延迟启动定时器")
        QTimer.singleShot(1000, delayed_start_services)

        # 运行Qt事件循环
        log_startup_step(startup_log, "LOOP", "开始Qt事件循环")
        logger.info("启动Qt事件循环")
        try:
            exit_code = app.exec()
        finally:
            log_startup_step(startup_log, "CLEANUP", "开始清理资源")
            cleanup_resources(config_manager, bridge, logger)
            log_startup_step(startup_log, "CLEANUP", "资源清理完成")

        log_startup_step(startup_log, "EXIT", f"程序正常退出，退出码: {exit_code}")
        sys.exit(exit_code)

    except ImportError as e:
        error_msg = f"导入模块失败: {e}"
        log_startup_step(startup_log, "ERROR", error_msg, False)
        print(f"❌ {error_msg}")
        print("请确保已安装所有依赖: pip install -r requirements.txt")
        traceback.print_exc()
        sys.exit(1)
    except Exception as e:
        error_msg = f"程序运行失败: {e}"
        log_startup_step(startup_log, "ERROR", error_msg, False)
        print(f"❌ {error_msg}")
        traceback.print_exc()

        # 如果有启动日志，记录完整的错误信息
        if startup_log:
            try:
                with open(startup_log, 'a', encoding='utf-8') as f:
                    f.write("\n" + "=" * 80 + "\n")
                    f.write("🚨 程序异常退出\n")
                    f.write("=" * 80 + "\n")
                    f.write(f"异常类型: {type(e).__name__}\n")
                    f.write(f"异常消息: {str(e)}\n")
                    f.write("堆栈跟踪:\n")
                    f.write(traceback.format_exc())
                    f.write("=" * 80 + "\n")
            except Exception:
                pass

        sys.exit(1)

if __name__ == "__main__":
    main()