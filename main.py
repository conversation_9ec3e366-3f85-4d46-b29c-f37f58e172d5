#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 雷电模拟器中控系统 - 重构后的主程序入口
========================================
功能描述: 简化的应用程序入口，使用统一的重构后系统
主要方法: main(), create_components(), connect_components()
调用关系: 程序入口点，创建并协调重构后的核心组件
注意事项:
- 使用简化配置管理器（SimpleConfigManager）
- 使用统一模拟器管理器（UnifiedEmulatorManager）
- 使用简化异步桥梁（SimpleAsyncBridge）
- 使用简化日志系统（基于Python标准logging）1
========================================
"""

import sys
import warnings
import argparse
from pathlib import Path

# 🎯 过滤PyQt6兼容性警告
warnings.filterwarnings("ignore", category=DeprecationWarning, message=".*sipPyTypeDict.*")

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="雷电模拟器中控系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py              # 正常启动
  python main.py --debug      # 启动诊断模式
  python main.py --debug --no-ui  # 仅运行诊断，不启动UI
        """
    )

    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用详细的启动诊断模式'
    )

    parser.add_argument(
        '--no-ui',
        action='store_true',
        help='仅运行诊断，不启动UI界面'
    )

    return parser.parse_args()


def run_startup_diagnostics_if_needed(debug_mode: bool, no_ui: bool = False):
    """根据参数运行启动诊断"""
    if debug_mode or no_ui:
        print("🔍 正在运行启动诊断...")

        try:
            from core.diagnostics import run_startup_diagnostics

            # 运行诊断
            diagnostics = run_startup_diagnostics(debug_mode=True)

            # 显示报告
            diagnostics.print_summary()

            # 如果只是诊断模式，检查后退出
            if no_ui:
                report = diagnostics.generate_report()
                if report['errors']:
                    print("\n❌ 发现关键错误，程序无法正常启动")
                    sys.exit(1)
                else:
                    print("\n✅ 诊断完成，程序可以正常启动")
                    sys.exit(0)

            # 如果有关键错误，询问是否继续
            report = diagnostics.generate_report()
            if report['errors']:
                print("\n⚠️ 发现关键错误，可能影响程序正常运行")
                response = input("是否继续启动? (y/N): ").strip().lower()
                if response not in ['y', 'yes']:
                    print("用户选择退出")
                    sys.exit(1)

            return diagnostics

        except Exception as e:
            print(f"❌ 诊断系统运行失败: {e}")
            if no_ui:
                sys.exit(1)
            return None

    return None


def setup_logging():
    """设置简化日志系统"""
    from core.logger_manager import setup_logging, log_runtime

    # 初始化简化日志系统
    setup_logging()
    log_runtime("应用程序启动", component="main")

    import logging
    return logging.getLogger(__name__)

def create_components(logger):
    """🎯 创建核心组件 - 删除重复的模拟器管理器返回"""
    from core.simple_config import get_config_manager
    from core.async_bridge import get_async_bridge

    # 🎯 创建统一配置管理器
    config_manager = get_config_manager()
    logger.info("统一配置管理器已创建")

    # 🎯 创建修复版异步桥梁（内部会创建模拟器管理器）
    bridge = get_async_bridge()
    logger.info("修复版异步桥梁已创建")

    return config_manager, bridge

def create_ui_components(config_manager, logger):
    """创建UI组件"""
    from core.logger_manager import log_runtime
    from ui.main_window_v2 import MainWindowV2
    main_window = MainWindowV2(config_manager=config_manager)
    logger.info("UI主窗口已创建")
    log_runtime("UI主窗口已创建", component="MainWindowV2")
    return main_window

def connect_components(main_window, bridge, logger):
    """🎯 连接UI和业务层组件 - 专注应用程序协调"""

    # 🎯 设置async_bridge引用，让UI可以访问
    main_window.async_bridge = bridge

    # 🎯 只负责UI和桥梁的连接，不涉及具体业务逻辑
    # 注意：batch_operation_requested信号连接在bridge.connect_task_signals_to_ui中处理，避免重复连接
    bridge.operation_completed.connect(main_window.on_operation_completed)
    bridge.operation_failed.connect(main_window.on_operation_failed)
    bridge.operation_progress.connect(main_window.on_operation_progress)

    # 🎯 通过桥梁连接所有组件，避免main.py直接操作业务层
    bridge.connect_task_signals_to_ui(main_window)
    bridge.connect_monitor_to_ui(main_window)

    # 🎯 关键修复：异步桥梁连接完成后，处理延迟的扫描请求
    if hasattr(main_window, 'process_pending_scan_requests'):
        main_window.process_pending_scan_requests()

    logger.info("UI层和业务层已连接")

def start_services(bridge, logger):
    """🎯 启动后台服务 - 通过桥梁启动，专注应用程序协调"""
    # 🎯 通过异步桥梁启动服务，不直接操作业务层
    bridge.start_background_services()
    logger.info("后台服务已启动")

def cleanup_resources(config_manager, bridge, logger):
    """清理应用程序资源 - 恢复异步桥梁版"""
    logger.info("开始清理资源...")

    # 保存配置
    try:
        config_manager.save()
        logger.info("配置已保存")
    except Exception as e:
        logger.error(f"配置保存失败: {e}")

    # 清理异步桥梁
    try:
        bridge.shutdown()
        logger.info("异步桥梁已关闭")
    except Exception as e:
        logger.error(f"异步桥梁关闭失败: {e}")

    logger.info("应用程序已退出")

# ========================================
# 🎯 主函数 - 应用程序生命周期管理
# ========================================
# 功能描述: 管理应用程序完整生命周期，从启动到关闭的所有流程
# 主要方法: main(), cleanup_resources()
# 调用关系: 程序入口点，协调所有组件的创建、连接、启动和清理
# 注意事项: 使用try-finally确保资源正确清理，支持异常处理和优雅退出
# ========================================

def show_auth_dialog():
    """🎯 显示认证对话框 - 按照官方指导"""
    from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                               QLineEdit, QComboBox, QCheckBox, QPushButton,
                               QTextEdit, QMessageBox, QApplication)
    from PyQt6.QtCore import Qt
    import json
    import os

    class SimpleAuthDialog(QDialog):
        def __init__(self):
            super().__init__()
            self.auth = None  # 🎯 延迟创建Auth实例
            self.auth_success = False
            self.setup_ui()
            self.load_saved_card()

            # 🎯 创建定时器用于线程安全的UI更新
            from PyQt6.QtCore import QTimer
            self.ui_update_timer = QTimer()
            self.ui_update_timer.timeout.connect(self._check_auth_result)
            self.auth_result = None

        def setup_ui(self):
            self.setWindowTitle("雷电模拟器中控系统 - 授权验证")
            self.setFixedSize(400, 350)
            self.setModal(True)

            layout = QVBoxLayout(self)

            # 标题
            title = QLabel("🎯 雷电模拟器中控系统")
            title.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
            layout.addWidget(title)

            # 卡号输入
            layout.addWidget(QLabel("授权卡号:"))
            self.card_input = QLineEdit()
            self.card_input.setPlaceholderText("请输入您的授权卡号")
            layout.addWidget(self.card_input)

            # 线路选择
            layout.addWidget(QLabel("主题风格:"))
            self.route_combo = QComboBox()
            self.route_combo.addItems([
                "风格1",
                "风格2",
                "风格3",
                "风格4"
            ])
            layout.addWidget(self.route_combo)

            # 记住卡密
            self.remember_checkbox = QCheckBox("记住卡密")
            layout.addWidget(self.remember_checkbox)

            # 公告显示
            layout.addWidget(QLabel("系统公告:"))
            self.notice_text = QTextEdit()
            self.notice_text.setFixedHeight(80)
            self.notice_text.setReadOnly(True)
            layout.addWidget(self.notice_text)

            # 按钮
            btn_layout = QHBoxLayout()
            self.verify_btn = QPushButton("开始验证")
            self.verify_btn.clicked.connect(self.verify_auth)
            btn_layout.addWidget(self.verify_btn)

            cancel_btn = QPushButton("取消")
            cancel_btn.clicked.connect(self.reject)
            btn_layout.addWidget(cancel_btn)

            layout.addLayout(btn_layout)

            # 🎯 设置默认公告，不在初始化时加载（避免延迟）
            self.notice_text.setPlainText("点击【开始验证】获取最新公告信息")

        def load_app_info(self):
            """🎯 延迟加载应用信息，只在验证时创建Auth实例"""
            self.notice_text.setPlainText("正在获取系统公告...")

            try:
                # 🎯 在需要时才创建Auth实例
                if self.auth is None:
                    from core.authsdk import Auth
                    self.auth = Auth()

                result = self.auth.appinfo(0)
                code = self.auth.rsa_decrypt(result['code'])
                if code == '200':
                    notice = self.auth.rsa_decrypt(result['notice'])
                    self.notice_text.setPlainText(notice)
            except Exception as e:
                print(f"获取应用信息失败: {e}")
                self.notice_text.setPlainText("获取公告失败")

        def load_saved_card(self):
            try:
                if os.path.exists('auth_config.json'):
                    with open('auth_config.json', 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        if config.get('remember_card'):
                            self.card_input.setText(config.get('card_number', ''))
                            self.remember_checkbox.setChecked(True)
                        self.route_combo.setCurrentIndex(config.get('route_index', 0))
            except:
                pass

        def save_card_config(self):
            try:
                config = {
                    'remember_card': self.remember_checkbox.isChecked(),
                    'card_number': self.card_input.text() if self.remember_checkbox.isChecked() else '',
                    'route_index': self.route_combo.currentIndex()
                }
                with open('auth_config.json', 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
            except:
                pass

        def verify_auth(self):
            card_number = self.card_input.text().strip()
            if not card_number:
                QMessageBox.warning(self, "错误", "请输入授权卡号")
                return

            # 🎯 使用线程避免UI卡顿
            self.verify_btn.setEnabled(False)
            self.verify_btn.setText("验证中...")

            # 获取线路设置
            route_index = self.route_combo.currentIndex()

            # 在后台线程中执行认证
            import threading
            auth_thread = threading.Thread(
                target=self._verify_in_background,
                args=(card_number, route_index),
                daemon=True
            )
            auth_thread.start()

            # 🎯 启动定时器检查结果
            self.ui_update_timer.start(100)  # 每100ms检查一次

        def _verify_in_background(self, card_number, route_index):
            """🎯 在后台线程中执行认证，避免UI卡顿"""
            try:
                # 🎯 在后台线程中创建Auth实例
                if self.auth is None:
                    from core.authsdk import Auth
                    self.auth = Auth()

                # 认证线路映射
                routes = [
                    "http://api.kushao.net/v3/",        # 风格1
                    "http://api.kushao.018888.xyz/v3/", # 风格2
                    "http://api.ks.186777.xyz/v3/",     # 风格3
                    "http://api.kushao.net/v3/"         # 风格4 (与风格1相同)
                ]
                self.auth.api = routes[route_index]

                # 🎯 并行执行公告获取和认证验证
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                    # 同时执行两个操作
                    future_info = executor.submit(self._load_app_info_background)
                    future_verify = executor.submit(self.auth.verify, card_number)

                    # 等待验证结果（公告可以后台继续加载）
                    result = future_verify.result()

                    # 尝试获取公告结果（如果还没完成就跳过）
                    try:
                        future_info.result(timeout=0.1)
                    except:
                        pass  # 公告获取可以继续在后台进行

                # 🎯 将结果存储，通过定时器更新UI
                self.auth_result = {
                    'type': 'result',
                    'data': result,
                    'card_number': card_number
                }

            except Exception as e:
                # 异常处理
                self.auth_result = {
                    'type': 'error',
                    'message': str(e)
                }

        def _load_app_info_background(self):
            """🎯 在后台线程中加载应用信息"""
            try:
                result = self.auth.appinfo(0)
                if result and 'code' in result:
                    code = self.auth.rsa_decrypt(result['code'])
                    if code == '200':
                        notice = self.auth.rsa_decrypt(result['notice'])
                        # 存储公告信息，通过定时器更新UI
                        self.notice_content = notice
            except Exception as e:
                print(f"获取应用信息失败: {e}")
                self.notice_content = "获取公告失败"

        def _check_auth_result(self):
            """🎯 定时器检查认证结果并更新UI"""
            if hasattr(self, 'notice_content'):
                self.notice_text.setPlainText(self.notice_content)
                delattr(self, 'notice_content')

            if self.auth_result is not None:
                result = self.auth_result
                self.auth_result = None
                self.ui_update_timer.stop()

                if result['type'] == 'result':
                    auth_result = result['data']
                    if auth_result['code'] == 200:
                        # 认证成功
                        self.save_card_config()
                        QMessageBox.information(self, "成功", auth_result['msg'])
                        self.auth_success = True

                        # 启动心跳检测
                        import threading
                        heartbeat_thread = threading.Thread(target=self.auth.heart_beat, daemon=True)
                        heartbeat_thread.start()

                        self.accept()
                    elif auth_result['code'] == 104:
                        # 换机处理
                        self.auth.change_device(result['card_number'])
                        QMessageBox.warning(self, "换机", "检测到换机，请重新验证")
                        self._reset_verify_button()
                    else:
                        # 认证失败 - 统一显示"验证失败"，不显示服务器具体错误信息
                        QMessageBox.critical(self, "失败", "验证失败")
                        self._reset_verify_button()
                elif result['type'] == 'error':
                    # 异常处理
                    QMessageBox.critical(self, "错误", f"验证过程出错: {result['message']}")
                    self._reset_verify_button()

        def _reset_verify_button(self):
            """🎯 重置验证按钮状态"""
            self.verify_btn.setEnabled(True)
            self.verify_btn.setText("开始验证")

        def _check_auth_result(self):
            """🎯 定时器检查认证结果并更新UI"""
            if hasattr(self, 'notice_content'):
                self.notice_text.setPlainText(self.notice_content)
                delattr(self, 'notice_content')

            if self.auth_result is not None:
                result = self.auth_result
                self.auth_result = None
                self.ui_update_timer.stop()

                if result['type'] == 'result':
                    auth_result = result['data']
                    if auth_result['code'] == 200:
                        # 认证成功
                        self.save_card_config()
                        QMessageBox.information(self, "成功", auth_result['msg'])
                        self.auth_success = True

                        # 启动心跳检测
                        import threading
                        heartbeat_thread = threading.Thread(target=self.auth.heart_beat, daemon=True)
                        heartbeat_thread.start()

                        self.accept()
                    elif auth_result['code'] == 104:
                        # 换机处理
                        self.auth.change_device(result['card_number'])
                        QMessageBox.warning(self, "换机", "检测到换机，请重新验证")
                        self._reset_verify_button()
                    else:
                        # 认证失败 - 统一显示"验证失败"，不显示服务器具体错误信息
                        QMessageBox.critical(self, "失败", "验证失败")
                        self._reset_verify_button()
                elif result['type'] == 'error':
                    # 异常处理
                    QMessageBox.critical(self, "错误", f"验证过程出错: {result['message']}")
                    self._reset_verify_button()

    # 显示认证对话框
    dialog = SimpleAuthDialog()
    if dialog.exec() == QDialog.DialogCode.Accepted and dialog.auth_success:
        return True
    else:
        return False

def main():
    """🎯 主函数 - 修复版启动序列，确保正确的初始化顺序"""
    try:
        # 解析命令行参数
        args = parse_arguments()

        # 运行启动诊断（如果需要）
        diagnostics = run_startup_diagnostics_if_needed(args.debug, args.no_ui)

        # 🎯 首先进行酷烧云认证验证
        from PyQt6.QtWidgets import QApplication
        temp_app = QApplication(sys.argv)

        if not show_auth_dialog():
            sys.exit(1)

        temp_app.quit()

        # 导入PyQt6
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTimer

        # 设置日志
        logger = setup_logging()
        if args.debug:
            logger.info("🔍 调试模式已启用")

        # 创建应用程序
        app = QApplication(sys.argv)
        logger.info("Qt应用程序已创建")

        # 创建核心组件
        config_manager, bridge = create_components(logger)

        # 创建UI组件
        main_window = create_ui_components(config_manager, logger)

        # 🎯 显示主窗口 - 在连接组件前显示，确保UI完全准备好
        main_window.show()
        logger.info("主窗口已显示")

        # 处理待处理的事件，确保UI完全渲染
        app.processEvents()

        # 连接组件 - UI显示后再连接
        connect_components(main_window, bridge, logger)

        # 🎯 延迟启动服务 - 确保UI和信号连接完全准备好
        def delayed_start_services():
            start_services(bridge, logger)
            logger.info("延迟启动服务完成")

        # 使用QTimer延迟1秒启动监控服务
        QTimer.singleShot(1000, delayed_start_services)

        # 运行Qt事件循环
        logger.info("启动Qt事件循环")
        try:
            exit_code = app.exec()
        finally:
            cleanup_resources(config_manager, bridge, logger)

        sys.exit(exit_code)

    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保已安装所有依赖: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()