[11:47:11.324] 🔍 开始显示认证对话框
[11:47:11.325] 🔍 开始创建认证对话框
[11:47:11.326] 🔍 开始初始化SimpleAuthDialog
[11:47:11.326] ✅ QDialog初始化完成
[11:47:11.327] ✅ 基本属性设置完成
[11:47:11.327] 🔍 开始设置UI
[11:47:11.327] ✅ 窗口标题设置完成
[11:47:11.327] ✅ 窗口大小设置完成
[11:47:11.328] ✅ 模态设置完成
[11:47:11.328] ✅ 主布局创建完成
[11:47:11.339] ✅ 标题标签创建完成
[11:47:11.344] ✅ 卡号输入框创建完成
[11:47:11.345] ✅ 线路选择框创建完成
[11:47:11.345] ✅ 记住卡密复选框创建完成
[11:47:11.346] ✅ 公告标签创建完成
[11:47:11.347] ✅ 公告文本框创建完成
[11:47:11.347] ✅ 验证按钮创建完成
[11:47:11.348] ✅ 取消按钮创建完成
[11:47:11.348] ✅ 按钮布局添加完成
[11:47:11.348] ✅ 默认公告设置完成
[11:47:11.349] ✅ UI设置完全成功
[11:47:11.349] ✅ UI设置完成
[11:47:11.350] ✅ 卡密加载完成
[11:47:11.351] ✅ 定时器设置完成
[11:47:11.351] ✅ SimpleAuthDialog初始化完全成功
[11:47:11.351] ✅ 认证对话框创建成功
[11:47:11.351] 🔍 开始显示对话框
[11:47:12.818] 🔍 开始验证认证
[11:47:12.819] ✅ 获取卡号: WpFz17Ov5vJk5GN6
[11:47:12.820] ✅ 按钮状态更新完成
[11:47:12.820] ✅ 获取线路索引: 2
[11:47:12.821] ✅ 准备创建认证线程
[11:47:12.822] ✅ 认证线程已启动
[11:47:12.822] 🔍 开始后台认证: card_number=WpFz17Ov5vJk5GN6, route_index=2
[11:47:12.822] ✅ 定时器已启动
[11:47:12.822] ✅ verify_auth完全成功
[11:47:12.822] 🔍 创建Auth实例...
[11:47:14.368] ✅ Auth实例创建成功: <class 'core.authsdk.Auth'>
[11:47:14.368] 🔍 设置认证路由: http://api.ks.186777.xyz/v3/
[11:47:14.368] 🔍 开始执行认证...
[11:47:14.369] 🔍 等待认证结果...
[11:47:15.538] 🔍 认证结果类型: <class 'dict'>
[11:47:15.538] 🔍 认证结果内容: {'code': 200, 'end_date': '2025-08-06 08:58:39', 'msg': '认证成功，有效期到：2025-08-06 08:58:39'}
[11:47:15.538] ✅ 认证执行完成，准备存储结果
[11:47:15.543] ✅ 认证结果已存储: None
[11:47:16.926] ✅ 对话框执行完成，结果: 1
[11:47:16.926] ✅ 认证成功，返回True
