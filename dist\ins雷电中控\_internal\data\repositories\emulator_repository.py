#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 模拟器数据仓库 - 直接数据库操作
========================================
功能描述: 模拟器数据的CRUD操作和业务查询
主要方法: save(), find_by_id(), find_all()
调用关系: 被UnifiedEmulatorManager调用，调用DatabaseManager
注意事项:
- 直接数据库操作，无缓存层
- 提供业务语义的数据操作接口
- 支持批量操作优化性能
- 简化架构，提高可维护性
========================================
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from data.database_manager import DatabaseManager, get_database_manager
from data.models.emulator_model import EmulatorModel
from core.logger_manager import log_info, log_error, log_performance


class EmulatorRepository:
    """模拟器数据仓库 - 提供业务语义的数据操作"""
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        """
        初始化模拟器仓库

        Args:
            db_manager: 数据库管理器实例，如果为None则使用全局实例
        """
        self.db_manager = db_manager or get_database_manager()

        log_info("模拟器数据仓库初始化完成", component="EmulatorRepository")
    
    def save(self, emulator: EmulatorModel) -> bool:
        """
        保存模拟器数据
        
        Args:
            emulator: 模拟器模型
            
        Returns:
            保存是否成功
        """
        if not emulator.validate():
            log_error(f"模拟器数据验证失败: {emulator}", component="EmulatorRepository")
            return False
        
        try:
            current_time = datetime.now().isoformat()
            
            # 检查是否已存在
            existing = self.find_by_id(emulator.id)
            
            if existing:
                # 更新现有记录 - 只更新用户配置字段
                sql = """
                    UPDATE emulators
                    SET name=?, path=?, cpu=?, memory=?, width=?, height=?, dpi=?,
                        assigned_tasks=?, priority=?, notes=?, updated_at=?
                    WHERE id=?
                """
                params = (
                    emulator.name, emulator.path, emulator.cpu, emulator.memory,
                    emulator.width, emulator.height, emulator.dpi,
                    emulator.assigned_tasks, emulator.priority, emulator.notes,
                    current_time, emulator.id
                )
            else:
                # 插入新记录 - 只插入用户配置字段
                sql = """
                    INSERT INTO emulators
                    (id, name, path, cpu, memory, width, height, dpi, assigned_tasks, priority, notes, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    emulator.id, emulator.name, emulator.path, emulator.cpu, emulator.memory,
                    emulator.width, emulator.height, emulator.dpi,
                    emulator.assigned_tasks, emulator.priority, emulator.notes,
                    current_time, current_time
                )
            
            success = self.db_manager.execute_transaction([(sql, params)])
            
            if success:
                log_info(f"模拟器保存成功: {emulator.name} (ID: {emulator.id})", component="EmulatorRepository")
            
            return success
            
        except Exception as e:
            log_error(f"保存模拟器失败: {e}", component="EmulatorRepository")
            return False
    
    def find_by_id(self, emulator_id: int) -> Optional[EmulatorModel]:
        """
        根据ID查找模拟器

        Args:
            emulator_id: 模拟器ID

        Returns:
            模拟器模型或None
        """
        try:
            results = self.db_manager.execute_query(
                "SELECT * FROM emulators WHERE id = ?",
                (emulator_id,)
            )

            if results:
                emulator = EmulatorModel.from_db_row(results[0])
                return emulator

            return None

        except Exception as e:
            log_error(f"查找模拟器失败 (ID: {emulator_id}): {e}", component="EmulatorRepository")
            return None
    
    def find_all(self) -> List[EmulatorModel]:
        """
        查找所有模拟器

        Returns:
            模拟器列表
        """
        try:
            results = self.db_manager.execute_query(
                "SELECT * FROM emulators ORDER BY id"
            )

            emulators = []
            for row in results:
                emulator = EmulatorModel.from_db_row(row)
                emulators.append(emulator)

            log_performance(f"查询所有模拟器完成: {len(emulators)}个", component="EmulatorRepository")
            return emulators

        except Exception as e:
            log_error(f"查找所有模拟器失败: {e}", component="EmulatorRepository")
            return []

    def get_all(self) -> List[Dict[str, Any]]:
        """获取所有模拟器 - 返回字典格式"""
        try:
            results = self.db_manager.execute_query(
                "SELECT * FROM emulators ORDER BY id"
            )
            log_performance(f"查询所有模拟器完成: {len(results)}个", component="EmulatorRepository")
            return results
        except Exception as e:
            log_error(f"查找所有模拟器失败: {e}", component="EmulatorRepository")
            return []

    # 🎯 删除运行时状态更新方法 - 状态通过实时检测获取，不存储在数据库
    
    # 🎯 删除批量状态更新方法 - 状态通过实时检测获取，不存储在数据库

    def delete(self, emulator_id: int) -> bool:
        """
        删除模拟器

        Args:
            emulator_id: 模拟器ID

        Returns:
            删除是否成功
        """
        try:
            success = self.db_manager.execute_transaction([
                ("DELETE FROM emulators WHERE id=?", (emulator_id,))
            ])

            if success:
                log_info(f"模拟器删除成功: ID {emulator_id}", component="EmulatorRepository")

            return success

        except Exception as e:
            log_error(f"删除模拟器失败 (ID: {emulator_id}): {e}", component="EmulatorRepository")
            return False

    def sync_from_scan(self, scanned_emulators: List[Dict[str, Any]]) -> bool:
        """
        从扫描结果同步模拟器数据

        Args:
            scanned_emulators: 扫描到的模拟器列表

        Returns:
            同步是否成功
        """
        try:
            # 使用数据库管理器的同步方法
            success = self.db_manager.sync_with_leidian(scanned_emulators)

            if success:
                log_info(f"模拟器数据同步完成: {len(scanned_emulators)}个", component="EmulatorRepository")

            return success

        except Exception as e:
            log_error(f"同步模拟器数据失败: {e}", component="EmulatorRepository")
            return False

    def batch_sync_from_scan(self, scanned_emulators: List[Dict[str, Any]]) -> bool:
        """
        批量同步扫描结果 - sync_from_scan的别名，用于性能优化

        Args:
            scanned_emulators: 扫描到的模拟器列表

        Returns:
            同步是否成功
        """
        return self.sync_from_scan(scanned_emulators)

    # 🎯 删除find_running方法 - 运行状态通过实时检测获取，不存储在数据库

    def get_count(self) -> int:
        """
        获取模拟器总数

        Returns:
            模拟器总数
        """
        try:
            results = self.db_manager.execute_query("SELECT COUNT(*) as count FROM emulators")
            return results[0]['count'] if results else 0
        except Exception as e:
            log_error(f"获取模拟器总数失败: {e}", component="EmulatorRepository")
            return 0

    def get_multiple_by_ids(self, emulator_ids: List[int]) -> Dict[int, Dict[str, Any]]:
        """🎯 批量获取模拟器信息 - 性能优化"""
        try:
            if not emulator_ids:
                return {}

            # 构建批量查询SQL
            placeholders = ','.join(['?' for _ in emulator_ids])
            query = f"SELECT * FROM emulators WHERE id IN ({placeholders})"

            cursor = self.db_manager.conn.execute(query, emulator_ids)
            rows = cursor.fetchall()

            # 转换为字典格式
            result = {}
            for row in rows:
                emulator_data = dict(row)
                result[emulator_data['id']] = emulator_data

            log_performance(f"批量获取{len(result)}个模拟器信息", component="EmulatorRepository")
            return result

        except Exception as e:
            log_error(f"批量获取模拟器失败: {e}", component="EmulatorRepository")
            return {}

    def update_multiple(self, emulator_data: Dict[int, Dict[str, Any]]) -> bool:
        """🎯 批量更新模拟器信息 - 性能优化"""
        try:
            if not emulator_data:
                return True

            # 开始事务
            self.db_manager.conn.execute("BEGIN TRANSACTION")

            for emulator_id, data in emulator_data.items():
                # 构建更新SQL
                set_clauses = []
                values = []

                for key, value in data.items():
                    if key != 'id':  # 不更新主键
                        set_clauses.append(f"{key} = ?")
                        values.append(value)

                if set_clauses:
                    values.append(emulator_id)
                    sql = f"UPDATE emulators SET {', '.join(set_clauses)}, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
                    self.db_manager.conn.execute(sql, values)

            # 提交事务
            self.db_manager.conn.commit()
            log_performance(f"批量更新{len(emulator_data)}个模拟器", component="EmulatorRepository")
            return True

        except Exception as e:
            # 回滚事务
            self.db_manager.conn.rollback()
            log_error(f"批量更新模拟器失败: {e}", component="EmulatorRepository")
            return False


# 🎯 全局模拟器仓库实例
_emulator_repository = None

def get_emulator_repository() -> EmulatorRepository:
    """获取全局模拟器仓库实例"""
    global _emulator_repository
    if _emulator_repository is None:
        _emulator_repository = EmulatorRepository()
    return _emulator_repository
