"""
启动诊断模块
提供详细的启动检查和错误诊断功能
"""

import sys
import os
import traceback
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path

class StartupDiagnostics:
    """启动诊断器"""
    
    def __init__(self, debug_mode: bool = False):
        self.debug_mode = debug_mode
        self.start_time = time.time()
        self.checks_performed = []
        self.errors_found = []
        self.warnings_found = []
        self.component_status = {}
        
        # 创建诊断日志文件
        self.log_file = self._create_log_file()
        
        if self.debug_mode:
            self.log("STARTUP", "DEBUG", "启动诊断模式已激活")
    
    def _create_log_file(self) -> Optional[str]:
        """创建诊断日志文件"""
        try:
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = log_dir / f"startup_debug_{timestamp}.log"
            
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"=== 启动诊断日志 ===\n")
                f.write(f"开始时间: {datetime.now()}\n")
                f.write(f"Python版本: {sys.version}\n")
                f.write(f"工作目录: {os.getcwd()}\n")
                f.write("=" * 50 + "\n\n")
            
            return str(log_file)
        except Exception as e:
            print(f"⚠️ 无法创建诊断日志文件: {e}")
            return None
    
    def log(self, component: str, level: str, message: str, details: Any = None):
        """记录诊断日志"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[STARTUP-{level}] [{timestamp}] [{component}] {message}"
        
        if self.debug_mode:
            print(log_entry)
        
        # 写入日志文件
        if self.log_file:
            try:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(log_entry + "\n")
                    if details:
                        f.write(f"    详细信息: {details}\n")
            except Exception:
                pass  # 静默处理日志写入错误
    
    def check_component(self, name: str, check_func: Callable, critical: bool = True) -> bool:
        """检查单个组件"""
        self.log(name, "INFO", f"开始检查组件: {name}")
        
        try:
            start_time = time.time()
            result = check_func()
            duration = time.time() - start_time
            
            if result:
                self.component_status[name] = "SUCCESS"
                self.log(name, "SUCCESS", f"组件检查通过 (耗时: {duration:.3f}s)")
                return True
            else:
                self.component_status[name] = "FAILED"
                error_msg = f"组件检查失败 (耗时: {duration:.3f}s)"
                self.log(name, "ERROR", error_msg)
                
                if critical:
                    self.errors_found.append(f"{name}: {error_msg}")
                else:
                    self.warnings_found.append(f"{name}: {error_msg}")
                return False
                
        except Exception as e:
            self.component_status[name] = "EXCEPTION"
            error_msg = f"组件检查异常: {str(e)}"
            self.log(name, "EXCEPTION", error_msg, traceback.format_exc())
            
            if critical:
                self.errors_found.append(f"{name}: {error_msg}")
            else:
                self.warnings_found.append(f"{name}: {error_msg}")
            return False
    
    def generate_report(self) -> Dict[str, Any]:
        """生成诊断报告"""
        total_time = time.time() - self.start_time
        
        report = {
            "startup_time": total_time,
            "timestamp": datetime.now().isoformat(),
            "component_status": self.component_status,
            "errors": self.errors_found,
            "warnings": self.warnings_found,
            "checks_performed": len(self.component_status),
            "success_rate": len([s for s in self.component_status.values() if s == "SUCCESS"]) / max(len(self.component_status), 1),
            "log_file": self.log_file
        }
        
        return report
    
    def print_summary(self):
        """打印诊断摘要"""
        report = self.generate_report()
        
        print("\n" + "=" * 60)
        print("🔍 启动诊断报告")
        print("=" * 60)
        print(f"总启动时间: {report['startup_time']:.3f}秒")
        print(f"检查组件数: {report['checks_performed']}")
        print(f"成功率: {report['success_rate']:.1%}")
        
        if report['errors']:
            print(f"\n❌ 发现 {len(report['errors'])} 个错误:")
            for error in report['errors']:
                print(f"  • {error}")
        
        if report['warnings']:
            print(f"\n⚠️ 发现 {len(report['warnings'])} 个警告:")
            for warning in report['warnings']:
                print(f"  • {warning}")
        
        if not report['errors'] and not report['warnings']:
            print("\n✅ 所有组件检查通过!")
        
        if self.log_file:
            print(f"\n📝 详细日志: {self.log_file}")
        
        print("=" * 60)


class ComponentChecker:
    """组件检查器基类"""
    
    def __init__(self, diagnostics: StartupDiagnostics):
        self.diagnostics = diagnostics
    
    def check_system_environment(self) -> bool:
        """检查系统环境"""
        try:
            # 检查Python版本
            if sys.version_info < (3, 8):
                self.diagnostics.log("SYSTEM", "ERROR", f"Python版本过低: {sys.version}")
                return False
            
            # 检查工作目录
            if not os.path.exists(os.getcwd()):
                self.diagnostics.log("SYSTEM", "ERROR", "工作目录不存在")
                return False
            
            # 检查关键目录
            required_dirs = ['core', 'config', 'img']
            for dir_name in required_dirs:
                if not os.path.exists(dir_name):
                    self.diagnostics.log("SYSTEM", "WARNING", f"缺少目录: {dir_name}")
            
            self.diagnostics.log("SYSTEM", "SUCCESS", "系统环境检查通过")
            return True
            
        except Exception as e:
            self.diagnostics.log("SYSTEM", "EXCEPTION", f"系统环境检查异常: {e}")
            return False
    
    def check_dependencies(self) -> bool:
        """检查依赖库"""
        required_modules = [
            'PyQt6', 'requests', 'psutil', 'sqlite3', 
            'cv2', 'PIL', 'numpy', 'win32gui'
        ]
        
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                self.diagnostics.log("DEPS", "SUCCESS", f"模块 {module} 可用")
            except ImportError:
                missing_modules.append(module)
                self.diagnostics.log("DEPS", "ERROR", f"缺少模块: {module}")
        
        if missing_modules:
            self.diagnostics.log("DEPS", "ERROR", f"缺少依赖: {', '.join(missing_modules)}")
            return False
        
        return True
    
    def check_config_files(self) -> bool:
        """检查配置文件"""
        try:
            # 检查主配置文件
            config_file = "app_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self.diagnostics.log("CONFIG", "SUCCESS", f"主配置文件加载成功: {len(config_data)} 项配置")
            else:
                self.diagnostics.log("CONFIG", "WARNING", "主配置文件不存在，将使用默认配置")
            
            # 检查配置目录
            config_dir = "config"
            if os.path.exists(config_dir):
                config_files = list(Path(config_dir).glob("*.json"))
                self.diagnostics.log("CONFIG", "INFO", f"发现 {len(config_files)} 个配置文件")
            
            return True
            
        except Exception as e:
            self.diagnostics.log("CONFIG", "ERROR", f"配置文件检查失败: {e}")
            return False

    def check_core_modules(self) -> bool:
        """检查核心模块"""
        core_modules = [
            'core.simple_config',
            'core.instagram_task',
            'core.leidianapi.LeiDian_Reorganized',
            'core.leidianapi.yijianzhaotu'
        ]

        failed_modules = []

        for module in core_modules:
            try:
                __import__(module)
                self.diagnostics.log("CORE", "SUCCESS", f"核心模块 {module} 导入成功")
            except Exception as e:
                failed_modules.append(f"{module}: {str(e)}")
                self.diagnostics.log("CORE", "ERROR", f"核心模块 {module} 导入失败: {e}")

        if failed_modules:
            self.diagnostics.log("CORE", "ERROR", f"核心模块导入失败: {len(failed_modules)} 个")
            return False

        return True

    def check_leidian_api(self) -> bool:
        """检查雷电API连接"""
        try:
            from core.leidianapi.LeiDian_Reorganized import Dnconsole

            # 尝试创建API实例（使用默认路径）
            import os
            base_path = os.path.join(os.environ.get('ProgramFiles', 'C:\\Program Files'), 'dnplayer')
            share_path = os.path.join(base_path, 'share')

            api = Dnconsole(base_path, share_path)
            self.diagnostics.log("LEIDIAN", "SUCCESS", "雷电API实例创建成功")

            # 检查雷电模拟器是否运行
            try:
                emulators = api.list2()  # 使用正确的方法名
                if emulators:
                    self.diagnostics.log("LEIDIAN", "SUCCESS", f"发现模拟器列表: {emulators}")
                else:
                    self.diagnostics.log("LEIDIAN", "WARNING", "未发现运行中的模拟器")
            except Exception as e:
                self.diagnostics.log("LEIDIAN", "WARNING", f"无法获取模拟器列表: {e}")

            return True

        except Exception as e:
            self.diagnostics.log("LEIDIAN", "ERROR", f"雷电API检查失败: {e}")
            return False

    def check_database_connection(self) -> bool:
        """检查数据库连接"""
        try:
            import sqlite3

            # 检查数据目录
            data_dir = Path("data")
            if not data_dir.exists():
                data_dir.mkdir(exist_ok=True)
                self.diagnostics.log("DATABASE", "INFO", "创建数据目录")

            # 测试SQLite连接
            test_db = data_dir / "test_connection.db"
            conn = sqlite3.connect(str(test_db))
            conn.execute("CREATE TABLE IF NOT EXISTS test (id INTEGER)")
            conn.close()

            # 清理测试文件
            if test_db.exists():
                test_db.unlink()

            self.diagnostics.log("DATABASE", "SUCCESS", "数据库连接测试通过")
            return True

        except Exception as e:
            self.diagnostics.log("DATABASE", "ERROR", f"数据库连接测试失败: {e}")
            return False

    def check_ui_components(self) -> bool:
        """检查UI组件"""
        try:
            from PyQt6.QtWidgets import QApplication
            from PyQt6.QtCore import QCoreApplication

            # 检查是否已有QApplication实例
            app = QCoreApplication.instance()
            if app is None:
                self.diagnostics.log("UI", "INFO", "未发现现有QApplication实例")
            else:
                self.diagnostics.log("UI", "SUCCESS", "发现现有QApplication实例")

            self.diagnostics.log("UI", "SUCCESS", "UI组件检查通过")
            return True

        except Exception as e:
            self.diagnostics.log("UI", "ERROR", f"UI组件检查失败: {e}")
            return False


def run_startup_diagnostics(debug_mode: bool = False) -> StartupDiagnostics:
    """运行完整的启动诊断"""
    diagnostics = StartupDiagnostics(debug_mode)
    checker = ComponentChecker(diagnostics)

    # 按顺序检查各个组件
    checks = [
        ("系统环境", checker.check_system_environment, True),
        ("依赖库", checker.check_dependencies, True),
        ("配置文件", checker.check_config_files, False),
        ("核心模块", checker.check_core_modules, True),
        ("雷电API", checker.check_leidian_api, False),  # 非关键，可选组件
        ("数据库连接", checker.check_database_connection, False),
        ("UI组件", checker.check_ui_components, True),
    ]

    for name, check_func, critical in checks:
        diagnostics.check_component(name, check_func, critical)

    return diagnostics
