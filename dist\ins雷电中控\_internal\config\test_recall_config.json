{"test_description": "Instagram私信撤回功能专项测试配置", "test_type": "recall_feature_test", "emulator_id": 2, "test_settings": {"timeout_seconds": 30, "retry_count": 3, "screenshot_on_error": true, "detailed_logging": true, "step_delay": 1}, "recall_test_config": {"recall_before_dm": true, "message_count": 1, "delay_min": 1000, "delay_max": 2000, "message_delay": 500, "send_mode": 1, "message_content_1": "测试撤回功能|Recall test message|撤回测试消息", "record_file_path": "test_recall_sent_users.txt"}, "test_scenarios": {"basic_recall": {"description": "基础撤回功能测试", "tests": ["test_recall_configuration", "test_long_press_functionality", "test_recall_confirmation_handling"], "expected_results": {"test_recall_configuration": true, "test_long_press_functionality": true, "test_recall_confirmation_handling": true}}, "message_detection": {"description": "消息检测功能测试", "tests": ["test_message_detection"], "expected_results": {"test_message_detection": true}}, "full_recall_flow": {"description": "完整撤回流程测试", "tests": ["test_full_recall_process", "test_recall_integration_in_dm_flow"], "expected_results": {"test_full_recall_process": true, "test_recall_integration_in_dm_flow": true}}}, "instagram_elements": {"direct_text_message": "com.instagram.android:id/direct_text_message_text_view", "message_input": "com.instagram.android:id/row_thread_composer_edittext", "send_button": "com.instagram.android:id/row_thread_composer_button_send", "profile_tab": "com.instagram.android:id/profile_tab", "followers_count": "com.instagram.android:id/row_profile_header_textview_followers_count"}, "test_instructions": {"preparation": ["确保模拟器{emulator_id}正在运行", "确保Instagram应用已登录", "确保有历史私信记录可供测试撤回", "建议在测试前备份重要数据"], "execution": ["运行: python test_instagram_recall_feature.py", "测试过程中会实际操作Instagram界面", "请勿在测试过程中手动操作模拟器", "测试结果将保存在test_results目录"], "verification": ["检查测试报告中的成功率", "验证撤回功能是否正常工作", "检查是否有异常或错误信息", "确认撤回操作不会影响其他功能"]}, "troubleshooting": {"common_issues": {"模拟器未运行": "请先启动模拟器{emulator_id}", "Instagram未登录": "请先登录Instagram账号", "无消息可撤回": "请先发送一些测试消息", "元素未找到": "可能Instagram版本不兼容，请检查元素ID", "长按失败": "检查坐标是否正确，屏幕是否可见"}, "debug_steps": ["检查模拟器状态", "验证Instagram应用状态", "检查元素定义是否正确", "查看详细日志信息", "截图分析界面状态"]}}